2025-07-10T02:28:50: 🚀 AI Product Support Agent running on port 3000
2025-07-10T02:28:50: 🌐 Web interface: http://localhost:3000
2025-07-10T02:28:50: 🤖 Telegram bot: Active
2025-07-10T02:28:50: 📊 Database: Connected
2025-07-10T02:28:50: 🧠 OpenAI: Connected
2025-07-10T02:28:50: ✅ Redis connected
2025-07-10T02:28:50: ✅ Redis connected
2025-07-10T02:28:50: 🔥 Warming up cache...
2025-07-10T02:28:50: 🔥 Warming up cache...
2025-07-10T02:28:50: ✅ C<PERSON> warmed up successfully
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-10T01:28:50.974Z"}
2025-07-10T02:28:50: ✅ Cache warmed up successfully
2025-07-10T02:28:50: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-10T01:28:50.974Z"}
2025-07-10T02:28:50: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-10T01:28:50.981Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-10T01:28:50.981Z"}
{"level":"error","message":"❌ Failed to connect to Weaviate:","service":"weaviate-service","timestamp":"2025-07-10T01:28:50.989Z"}
2025-07-10T02:28:50: [31merror[39m: ❌ Failed to connect to Weaviate: {"service":"weaviate-service","timestamp":"2025-07-10T01:28:50.989Z"}
{"level":"warn","message":"⚠️ Weaviate not available, using Supabase only","service":"weaviate-service","timestamp":"2025-07-10T01:28:50.990Z"}
{"level":"error","message":"❌ Failed to connect to Weaviate:","service":"weaviate-service","timestamp":"2025-07-10T01:28:50.992Z"}
{"level":"warn","message":"⚠️ Weaviate not available, using Supabase only","service":"weaviate-service","timestamp":"2025-07-10T01:28:50.992Z"}
2025-07-10T02:28:50: [33mwarn[39m: ⚠️ Weaviate not available, using Supabase only {"service":"weaviate-service","timestamp":"2025-07-10T01:28:50.990Z"}
2025-07-10T02:28:50: ✅ Telegram bot: Hybrid search initialized
2025-07-10T02:28:50: [31merror[39m: ❌ Failed to connect to Weaviate: {"service":"weaviate-service","timestamp":"2025-07-10T01:28:50.992Z"}
2025-07-10T02:28:50: [33mwarn[39m: ⚠️ Weaviate not available, using Supabase only {"service":"weaviate-service","timestamp":"2025-07-10T01:28:50.992Z"}
2025-07-10T02:28:51: Database connection test failed: TypeError: fetch failed
2025-07-10T02:28:51: Tables may not exist yet - this is normal on first run
2025-07-10T02:28:51: Webhook set successfully
2025-07-10T02:28:51: Telegram bot webhook set up
2025-07-10T02:28:51: All services initialized successfully
2025-07-10T10:40:35: 📨 Webhook received: 2025-07-10T09:40:35.522Z
2025-07-10T10:40:35: 📨 Request body: {
  "update_id": 706613076,
  "message": {
    "message_id": 1385,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752140435,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-10T10:40:35: 🗑️ Clearing cache pattern: /start
2025-07-10T10:40:40: 📨 Webhook received: 2025-07-10T09:40:40.717Z
2025-07-10T10:40:40: 📨 Request body: {
  "update_id": 706613077,
  "callback_query": {
    "id": "1809919224122079109",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1386,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752140435,
      "text": "🤖 Hi! I'm your Samsung Display Assistant.\n\nQuick setup - what brings you here today?\n\n💡 **Pro tip**: Type your model (WA65C, WM75B, etc.) for instant specs!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔍 I have a specific model",
              "callback_data": "have_model"
            }
          ],
          [
            {
              "text": "📱 Help me find my model",
              "callback_data": "find_model"
            }
          ],
          [
            {
              "text": "❓ General questions",
              "callback_data": "general_help"
            }
          ],
          [
            {
              "text": "📚 Browse all models",
              "callback_data": "browse_models"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "find_model"
  }
}
2025-07-10T10:40:43: 📨 Webhook received: 2025-07-10T09:40:43.015Z
2025-07-10T10:40:43: 📨 Request body: {
  "update_id": 706613078,
  "callback_query": {
    "id": "1809919222415508004",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1387,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752140440,
      "text": "I'll help you identify your model! What do you know about your display?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "📱 Android displays (WA series)",
              "callback_data": "android_series"
            }
          ],
          [
            {
              "text": "💻 Tizen displays (WM series)",
              "callback_data": "tizen_series"
            }
          ],
          [
            {
              "text": "📷 Send photo of label",
              "callback_data": "photo_help"
            }
          ],
          [
            {
              "text": "📏 Tell me the size",
              "callback_data": "size_help"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "android_series"
  }
}
2025-07-10T10:40:47: 📨 Webhook received: 2025-07-10T09:40:47.070Z
2025-07-10T10:40:47: 📨 Request body: {
  "update_id": 706613079,
  "callback_query": {
    "id": "1809919220693823515",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1388,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752140443,
      "text": "📱 **Android Display Series (WA)**\n\nThese displays run Android and support Google Play Store apps:\n\n• **WA-C Series**: Standard Android displays\n• **WA-D Series**: Enhanced Android with Google EDLA\n• **WA-F Series**: Latest Android displays\n\nWhich model do you have?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "WA65C (65\")",
              "callback_data": "model_WA65C"
            },
            {
              "text": "WA75C (75\")",
              "callback_data": "model_WA75C"
            }
          ],
          [
            {
              "text": "WA65D (65\")",
              "callback_data": "model_WA65D"
            },
            {
              "text": "WA75D (75\")",
              "callback_data": "model_WA75D"
            }
          ],
          [
            {
              "text": "WA65F (65\")",
              "callback_data": "model_WA65F"
            },
            {
              "text": "WA75F (75\")",
              "callback_data": "model_WA75F"
            }
          ],
          [
            {
              "text": "📱 All Android models",
              "callback_data": "all_android"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "model_WA65D"
  }
}
2025-07-10T10:40:50: 📨 Webhook received: 2025-07-10T09:40:50.815Z
2025-07-10T10:40:50: 📨 Request body: {
  "update_id": 706613080,
  "callback_query": {
    "id": "1809919223673226467",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1389,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752140447,
      "text": "📱 WA65D - Samsung WAD\n\n🖥️ Display: 65 inch UHD\n💻 OS: Android 13\n🔌 OPS Support: Yes\n📺 HDMI Ports: 3\n📱 Google Play Store: Yes\n🌐 Browser: Built-in\n👆 Touch: 40-point\n🎯 Target: Education,Corporate\n\n💡 Tap buttons below for detailed specs or ask me anything!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔌 Connectivity",
              "callback_data": "specs_connectivity_WA65D"
            }
          ],
          [
            {
              "text": "📱 Software",
              "callback_data": "specs_software_WA65D"
            }
          ],
          [
            {
              "text": "📏 Physical",
              "callback_data": "specs_physical_WA65D"
            }
          ],
          [
            {
              "text": "🛠️ Setup Guide",
              "callback_data": "setup_WA65D"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "specs_connectivity_WA65D"
  }
}
2025-07-10T10:41:09: 📨 Webhook received: 2025-07-10T09:41:09.293Z
2025-07-10T10:41:09: 📨 Request body: {
  "update_id": 706613081,
  "message": {
    "message_id": 1391,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752140469,
    "text": "Contacted this to wa65f"
  }
}
2025-07-10T10:41:09: 🔍 Detected product: WA65F (Samsung)
2025-07-10T10:41:09: ✅ Created user device tracking for 6863855601: WA65F
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-10T09:41:11.096Z"}
2025-07-10T10:41:11: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-10T09:41:11.096Z"}
2025-07-10T10:41:24: 📨 Webhook received: 2025-07-10T09:41:24.589Z
2025-07-10T10:41:24: 📨 Request body: {
  "update_id": 706613082,
  "message": {
    "message_id": 1394,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752140484,
    "text": "Compare the models"
  }
}
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-10T09:41:25.991Z"}
2025-07-10T10:41:25: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-10T09:41:25.991Z"}
2025-07-10T10:41:50: 📨 Webhook received: 2025-07-10T09:41:50.046Z
2025-07-10T10:41:50: 📨 Request body: {
  "update_id": 706613083,
  "message": {
    "message_id": 1397,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752140510,
    "text": "Send thr manuals"
  }
}
2025-07-10T10:41:50: 📚 Found 4 total documents
2025-07-10T10:41:50: 📚 sendDocumentDownloads: Processing 4 documents
2025-07-10T10:41:50: 📚 After filtering: 4 relevant documents
2025-07-11T09:12:08: 📨 Webhook received: 2025-07-11T08:12:08.979Z
2025-07-11T09:12:08: 📨 Request body: {
  "update_id": 706613084,
  "message": {
    "message_id": 1400,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752221528,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-11T09:12:08: 🗑️ Clearing cache pattern: /start
2025-07-11T09:12:17: 📨 Webhook received: 2025-07-11T08:12:17.391Z
2025-07-11T09:12:17: 📨 Request body: {
  "update_id": 706613085,
  "callback_query": {
    "id": "1809919222514972163",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1401,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752221529,
      "text": "🤖 Hi! I'm your Samsung Display Assistant.\n\nQuick setup - what brings you here today?\n\n💡 **Pro tip**: Type your model (WA65C, WM75B, etc.) for instant specs!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔍 I have a specific model",
              "callback_data": "have_model"
            }
          ],
          [
            {
              "text": "📱 Help me find my model",
              "callback_data": "find_model"
            }
          ],
          [
            {
              "text": "❓ General questions",
              "callback_data": "general_help"
            }
          ],
          [
            {
              "text": "📚 Browse all models",
              "callback_data": "browse_models"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "have_model"
  }
}
2025-07-11T09:12:24: 📨 Webhook received: 2025-07-11T08:12:24.493Z
2025-07-11T09:12:24: 📨 Request body: {
  "update_id": 706613086,
  "message": {
    "message_id": 1403,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752221544,
    "text": "Wm55b"
  }
}
2025-07-11T09:12:24: 🔍 Detected product: WM55B (Samsung)
2025-07-11T09:12:24: ✅ Updated user device tracking for 6863855601: WM55B
2025-07-11T09:12:27: 📨 Webhook received: 2025-07-11T08:12:27.209Z
2025-07-11T09:12:27: 📨 Request body: {
  "update_id": 706613087,
  "callback_query": {
    "id": "1809919221433461684",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1404,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752221544,
      "text": "📱 WM55B - Samsung WMB\n\n🖥️ Display: 55 inch UHD\n💻 OS: Tizen\n🔌 OPS Support: No\n📺 HDMI Ports: 1\n📱 Google Play Store: No\n🌐 Browser: Built-in\n👆 Touch: 20-point\n🎯 Target: Corporate,Meeting Rooms\n\n💡 Tap buttons below for detailed specs or ask me anything!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔌 Connectivity",
              "callback_data": "specs_connectivity_WM55B"
            }
          ],
          [
            {
              "text": "📱 Software",
              "callback_data": "specs_software_WM55B"
            }
          ],
          [
            {
              "text": "📏 Physical",
              "callback_data": "specs_physical_WM55B"
            }
          ],
          [
            {
              "text": "🛠️ Setup Guide",
              "callback_data": "setup_WM55B"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "specs_connectivity_WM55B"
  }
}
2025-07-11T09:12:35: 📨 Webhook received: 2025-07-11T08:12:35.311Z
2025-07-11T09:12:35: 📨 Request body: {
  "update_id": 706613088,
  "message": {
    "message_id": 1406,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752221555,
    "text": "Send me the manual"
  }
}
2025-07-11T09:12:35: 📚 Found 4 total documents
2025-07-11T09:12:35: 📚 sendDocumentDownloads: Processing 4 documents
2025-07-11T09:12:35: 📚 After filtering: 4 relevant documents
2025-07-11T09:12:52: 📨 Webhook received: 2025-07-11T08:12:52.239Z
2025-07-11T09:12:52: 📨 Request body: {
  "update_id": 706613089,
  "message": {
    "message_id": 1409,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752221572,
    "text": "Compare this model to wa65c"
  }
}
2025-07-11T09:12:52: 🔍 Detected product: WA65C (Samsung)
2025-07-11T09:12:52: ✅ Updated user device tracking for 6863855601: WA65C
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-11T08:12:54.302Z"}
2025-07-11T09:12:54: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-11T08:12:54.302Z"}
2025-07-11T09:13:56: 📨 Webhook received: 2025-07-11T08:13:56.306Z
2025-07-11T09:13:56: 📨 Request body: {
  "update_id": 706613090,
  "message": {
    "message_id": 1412,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752221636,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-11T09:13:56: 🗑️ Clearing cache pattern: /start
2025-07-11T09:14:00: 📨 Webhook received: 2025-07-11T08:14:00.257Z
2025-07-11T09:14:00: 📨 Request body: {
  "update_id": 706613091,
  "callback_query": {
    "id": "1809919223973134368",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1413,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752221636,
      "text": "🤖 Hi! I'm your Samsung Display Assistant.\n\nQuick setup - what brings you here today?\n\n💡 **Pro tip**: Type your model (WA65C, WM75B, etc.) for instant specs!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔍 I have a specific model",
              "callback_data": "have_model"
            }
          ],
          [
            {
              "text": "📱 Help me find my model",
              "callback_data": "find_model"
            }
          ],
          [
            {
              "text": "❓ General questions",
              "callback_data": "general_help"
            }
          ],
          [
            {
              "text": "📚 Browse all models",
              "callback_data": "browse_models"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "find_model"
  }
}
2025-07-11T09:14:02: 📨 Webhook received: 2025-07-11T08:14:02.999Z
2025-07-11T09:14:02: 📨 Request body: {
  "update_id": 706613092,
  "callback_query": {
    "id": "1809919223180435253",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1414,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752221640,
      "text": "I'll help you identify your model! What do you know about your display?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "📱 Android displays (WA series)",
              "callback_data": "android_series"
            }
          ],
          [
            {
              "text": "💻 Tizen displays (WM series)",
              "callback_data": "tizen_series"
            }
          ],
          [
            {
              "text": "📷 Send photo of label",
              "callback_data": "photo_help"
            }
          ],
          [
            {
              "text": "📏 Tell me the size",
              "callback_data": "size_help"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "tizen_series"
  }
}
2025-07-11T09:14:05: 📨 Webhook received: 2025-07-11T08:14:05.064Z
2025-07-11T09:14:05: 📨 Request body: {
  "update_id": 706613093,
  "callback_query": {
    "id": "1809919220792146858",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1415,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752221643,
      "text": "💻 **Tizen Display Series (WM)**\n\nThese displays run Samsung's Tizen OS and are designed for business use:\n\n• **WM-B Series**: Digital flipcharts with touch\n• Built-in whiteboard applications\n• Corporate meeting room solutions\n\nWhich model do you have?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "WM55B (55\")",
              "callback_data": "model_WM55B"
            },
            {
              "text": "WM65B (65\")",
              "callback_data": "model_WM65B"
            }
          ],
          [
            {
              "text": "WM75B (75\")",
              "callback_data": "model_WM75B"
            },
            {
              "text": "WM85B (85\")",
              "callback_data": "model_WM85B"
            }
          ],
          [
            {
              "text": "💻 All Tizen models",
              "callback_data": "all_tizen"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "model_WM65B"
  }
}
2025-07-11T09:14:07: 📨 Webhook received: 2025-07-11T08:14:07.274Z
2025-07-11T09:14:07: 📨 Request body: {
  "update_id": 706613094,
  "callback_query": {
    "id": "1809919221143398420",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1416,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752221645,
      "text": "📱 WM65B - Samsung WMB\n\n🖥️ Display: 65 inch UHD\n💻 OS: Tizen\n🔌 OPS Support: No\n📺 HDMI Ports: 1\n📱 Google Play Store: No\n🌐 Browser: Built-in\n👆 Touch: 20-point\n🎯 Target: Corporate,Meeting Rooms\n\n💡 Tap buttons below for detailed specs or ask me anything!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔌 Connectivity",
              "callback_data": "specs_connectivity_WM65B"
            }
          ],
          [
            {
              "text": "📱 Software",
              "callback_data": "specs_software_WM65B"
            }
          ],
          [
            {
              "text": "📏 Physical",
              "callback_data": "specs_physical_WM65B"
            }
          ],
          [
            {
              "text": "🛠️ Setup Guide",
              "callback_data": "setup_WM65B"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "specs_software_WM65B"
  }
}
2025-07-11T09:14:13: 📨 Webhook received: 2025-07-11T08:14:13.341Z
2025-07-11T09:14:13: 📨 Request body: {
  "update_id": 706613095,
  "message": {
    "message_id": 1418,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752221653,
    "text": "Send the manual"
  }
}
2025-07-11T09:14:13: 📚 Found 4 total documents
2025-07-11T09:14:13: 📚 sendDocumentDownloads: Processing 4 documents
2025-07-11T09:14:13: 📚 After filtering: 4 relevant documents
2025-07-11T12:07:37: 📨 Webhook received: 2025-07-11T11:07:37.175Z
2025-07-11T12:07:37: 📨 Request body: {
  "update_id": 706613096,
  "message": {
    "message_id": 1421,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752232057,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-11T12:07:37: 🗑️ Clearing cache pattern: /start
2025-07-11T12:07:41: 📨 Webhook received: 2025-07-11T11:07:41.416Z
2025-07-11T12:07:41: 📨 Request body: {
  "update_id": 706613097,
  "message": {
    "message_id": 1423,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752232061,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-11T12:07:44: 📨 Webhook received: 2025-07-11T11:07:44.199Z
2025-07-11T12:07:44: 📨 Request body: {
  "update_id": 706613098,
  "message": {
    "message_id": 1425,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752232064,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-11T12:07:49: 📨 Webhook received: 2025-07-11T11:07:49.652Z
2025-07-11T12:07:49: 📨 Request body: {
  "update_id": 706613099,
  "message": {
    "message_id": 1427,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752232069,
    "text": "/devices",
    "entities": [
      {
        "offset": 0,
        "length": 8,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-11T12:46:47: 📨 Webhook received: 2025-07-11T11:46:47.385Z
2025-07-11T12:46:47: 📨 Request body: {
  "update_id": 706613100,
  "message": {
    "message_id": 1429,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752234407,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-11T12:46:47: 🗑️ Clearing cache pattern: /start
2025-07-11T12:46:50: 📨 Webhook received: 2025-07-11T11:46:50.570Z
2025-07-11T12:46:50: 📨 Request body: {
  "update_id": 706613101,
  "callback_query": {
    "id": "1809919224086325885",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1430,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752234407,
      "text": "🤖 Hi! I'm your Samsung Display Assistant.\n\nQuick setup - what brings you here today?\n\n💡 **Pro tip**: Type your model (WA65C, WM75B, etc.) for instant specs!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔍 I have a specific model",
              "callback_data": "have_model"
            }
          ],
          [
            {
              "text": "📱 Help me find my model",
              "callback_data": "find_model"
            }
          ],
          [
            {
              "text": "❓ General questions",
              "callback_data": "general_help"
            }
          ],
          [
            {
              "text": "📚 Browse all models",
              "callback_data": "browse_models"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "find_model"
  }
}
2025-07-11T12:46:52: 📨 Webhook received: 2025-07-11T11:46:52.681Z
2025-07-11T12:46:52: 📨 Request body: {
  "update_id": 706613102,
  "callback_query": {
    "id": "1809919222623362139",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1431,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752234410,
      "text": "I'll help you identify your model! What do you know about your display?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "📱 Android displays (WA series)",
              "callback_data": "android_series"
            }
          ],
          [
            {
              "text": "💻 Tizen displays (WM series)",
              "callback_data": "tizen_series"
            }
          ],
          [
            {
              "text": "📷 Send photo of label",
              "callback_data": "photo_help"
            }
          ],
          [
            {
              "text": "📏 Tell me the size",
              "callback_data": "size_help"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "android_series"
  }
}
2025-07-11T12:46:54: 📨 Webhook received: 2025-07-11T11:46:54.548Z
2025-07-11T12:46:54: 📨 Request body: {
  "update_id": 706613103,
  "callback_query": {
    "id": "1809919222804462802",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1432,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752234412,
      "text": "📱 **Android Display Series (WA)**\n\nThese displays run Android and support Google Play Store apps:\n\n• **WA-C Series**: Standard Android displays\n• **WA-D Series**: Enhanced Android with Google EDLA\n• **WA-F Series**: Latest Android displays\n\nWhich model do you have?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "WA65C (65\")",
              "callback_data": "model_WA65C"
            },
            {
              "text": "WA75C (75\")",
              "callback_data": "model_WA75C"
            }
          ],
          [
            {
              "text": "WA65D (65\")",
              "callback_data": "model_WA65D"
            },
            {
              "text": "WA75D (75\")",
              "callback_data": "model_WA75D"
            }
          ],
          [
            {
              "text": "WA65F (65\")",
              "callback_data": "model_WA65F"
            },
            {
              "text": "WA75F (75\")",
              "callback_data": "model_WA75F"
            }
          ],
          [
            {
              "text": "📱 All Android models",
              "callback_data": "all_android"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "model_WA75F"
  }
}
2025-07-11T12:46:56: 📨 Webhook received: 2025-07-11T11:46:56.750Z
2025-07-11T12:46:56: 📨 Request body: {
  "update_id": 706613104,
  "callback_query": {
    "id": "1809919221760240733",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1433,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752234414,
      "text": "📱 WA75F - Samsung WAF\n\n🖥️ Display: 75 inch UHD\n💻 OS: Android 14\n🔌 OPS Support: Yes\n📺 HDMI Ports: 2\n📱 Google Play Store: Yes\n🌐 Browser: Built-in\n👆 Touch: 40-point\n🎯 Target: Education,Corporate\n\n💡 Tap buttons below for detailed specs or ask me anything!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔌 Connectivity",
              "callback_data": "specs_connectivity_WA75F"
            }
          ],
          [
            {
              "text": "📱 Software",
              "callback_data": "specs_software_WA75F"
            }
          ],
          [
            {
              "text": "📏 Physical",
              "callback_data": "specs_physical_WA75F"
            }
          ],
          [
            {
              "text": "🛠️ Setup Guide",
              "callback_data": "setup_WA75F"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "specs_connectivity_WA75F"
  }
}
2025-07-11T12:47:00: 📨 Webhook received: 2025-07-11T11:47:00.992Z
2025-07-11T12:47:00: 📨 Request body: {
  "update_id": 706613105,
  "callback_query": {
    "id": "1809919222597241188",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1433,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752234414,
      "text": "📱 WA75F - Samsung WAF\n\n🖥️ Display: 75 inch UHD\n💻 OS: Android 14\n🔌 OPS Support: Yes\n📺 HDMI Ports: 2\n📱 Google Play Store: Yes\n🌐 Browser: Built-in\n👆 Touch: 40-point\n🎯 Target: Education,Corporate\n\n💡 Tap buttons below for detailed specs or ask me anything!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔌 Connectivity",
              "callback_data": "specs_connectivity_WA75F"
            }
          ],
          [
            {
              "text": "📱 Software",
              "callback_data": "specs_software_WA75F"
            }
          ],
          [
            {
              "text": "📏 Physical",
              "callback_data": "specs_physical_WA75F"
            }
          ],
          [
            {
              "text": "🛠️ Setup Guide",
              "callback_data": "setup_WA75F"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "specs_software_WA75F"
  }
}
2025-07-11T12:47:11: 📨 Webhook received: 2025-07-11T11:47:11.766Z
2025-07-11T12:47:11: 📨 Request body: {
  "update_id": 706613106,
  "message": {
    "message_id": 1436,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752234431,
    "text": "Send me a manual"
  }
}
2025-07-11T12:47:11: 📚 Found 4 total documents
2025-07-11T12:47:11: 📚 sendDocumentDownloads: Processing 4 documents
2025-07-11T12:47:11: 📚 After filtering: 4 relevant documents
2025-07-11T12:47:25: 📨 Webhook received: 2025-07-11T11:47:25.863Z
2025-07-11T12:47:25: 📨 Request body: {
  "update_id": 706613107,
  "message": {
    "message_id": 1439,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752234445,
    "text": "The screen isn't turning on"
  }
}
2025-07-11T12:47:28: ✅ Created support issue for user 6863855601: Display Problem
2025-07-11T12:47:28: 🎟️ Created support issue: Display Problem - Display Issues
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-11T11:47:28.391Z"}
2025-07-11T12:47:28: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-11T11:47:28.391Z"}
2025-07-17T15:37:19: 📨 Webhook received: 2025-07-17T14:37:19.852Z
2025-07-17T15:37:19: 📨 Request body: {
  "update_id": 706613108,
  "message": {
    "message_id": 1442,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752763039,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-17T15:37:19: 🗑️ Clearing cache pattern: /start
2025-07-17T15:37:24: 📨 Webhook received: 2025-07-17T14:37:24.054Z
2025-07-17T15:37:24: 📨 Request body: {
  "update_id": 706613109,
  "callback_query": {
    "id": "1809919222797802622",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1443,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752763039,
      "text": "🤖 Hi! I'm your Samsung Display Assistant.\n\nQuick setup - what brings you here today?\n\n💡 **Pro tip**: Type your model (WA65C, WM75B, etc.) for instant specs!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔍 I have a specific model",
              "callback_data": "have_model"
            }
          ],
          [
            {
              "text": "📱 Help me find my model",
              "callback_data": "find_model"
            }
          ],
          [
            {
              "text": "❓ General questions",
              "callback_data": "general_help"
            }
          ],
          [
            {
              "text": "📚 Browse all models",
              "callback_data": "browse_models"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "find_model"
  }
}
2025-07-17T15:37:26: 📨 Webhook received: 2025-07-17T14:37:26.178Z
2025-07-17T15:37:26: 📨 Request body: {
  "update_id": 706613110,
  "callback_query": {
    "id": "1809919221671125808",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1444,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752763044,
      "text": "I'll help you identify your model! What do you know about your display?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "📱 Android displays (WA series)",
              "callback_data": "android_series"
            }
          ],
          [
            {
              "text": "💻 Tizen displays (WM series)",
              "callback_data": "tizen_series"
            }
          ],
          [
            {
              "text": "📷 Send photo of label",
              "callback_data": "photo_help"
            }
          ],
          [
            {
              "text": "📏 Tell me the size",
              "callback_data": "size_help"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "tizen_series"
  }
}
2025-07-17T15:37:27: 📨 Webhook received: 2025-07-17T14:37:27.755Z
2025-07-17T15:37:27: 📨 Request body: {
  "update_id": 706613111,
  "callback_query": {
    "id": "1809919223008169537",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1445,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752763046,
      "text": "💻 **Tizen Display Series (WM)**\n\nThese displays run Samsung's Tizen OS and are designed for business use:\n\n• **WM-B Series**: Digital flipcharts with touch\n• Built-in whiteboard applications\n• Corporate meeting room solutions\n\nWhich model do you have?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "WM55B (55\")",
              "callback_data": "model_WM55B"
            },
            {
              "text": "WM65B (65\")",
              "callback_data": "model_WM65B"
            }
          ],
          [
            {
              "text": "WM75B (75\")",
              "callback_data": "model_WM75B"
            },
            {
              "text": "WM85B (85\")",
              "callback_data": "model_WM85B"
            }
          ],
          [
            {
              "text": "💻 All Tizen models",
              "callback_data": "all_tizen"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "model_WM75B"
  }
}
2025-07-17T15:37:29: 📨 Webhook received: 2025-07-17T14:37:29.360Z
2025-07-17T15:37:29: 📨 Request body: {
  "update_id": 706613112,
  "callback_query": {
    "id": "1809919224468352008",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1446,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752763047,
      "text": "📱 WM75B - Samsung WMB\n\n🖥️ Display: 75 inch UHD\n💻 OS: Tizen\n🔌 OPS Support: Yes\n📺 HDMI Ports: 1\n📱 Google Play Store: No\n🌐 Browser: Built-in\n👆 Touch: 20-point\n🎯 Target: Corporate,Meeting Rooms\n\n💡 Tap buttons below for detailed specs or ask me anything!",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "🔌 Connectivity",
              "callback_data": "specs_connectivity_WM75B"
            }
          ],
          [
            {
              "text": "📱 Software",
              "callback_data": "specs_software_WM75B"
            }
          ],
          [
            {
              "text": "📏 Physical",
              "callback_data": "specs_physical_WM75B"
            }
          ],
          [
            {
              "text": "🛠️ Setup Guide",
              "callback_data": "setup_WM75B"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "specs_software_WM75B"
  }
}
2025-07-19T13:05:03: 📨 Webhook received: 2025-07-19T12:05:03.011Z
2025-07-19T13:05:03: 📨 Request body: {
  "update_id": 706613113,
  "message": {
    "message_id": 1448,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752926702,
    "text": "Hi"
  }
}
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-19T12:05:04.219Z"}
2025-07-19T13:05:04: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-19T12:05:04.219Z"}
2025-07-19T13:41:41: 📨 Webhook received: 2025-07-19T12:41:41.955Z
2025-07-19T13:41:41: 📨 Request body: {
  "update_id": 706613114,
  "message": {
    "message_id": 1451,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752928901,
    "text": "There's a problem mate"
  }
}
2025-07-19T13:41:55: 📨 Webhook received: 2025-07-19T12:41:55.887Z
2025-07-19T13:41:55: 📨 Request body: {
  "update_id": 706613115,
  "callback_query": {
    "id": "1809919224243098909",
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "message": {
      "message_id": 1452,
      "from": {
        "id": 7586114824,
        "is_bot": true,
        "first_name": "Interactive support",
        "username": "samsungtouchscreen_bot"
      },
      "chat": {
        "id": 6863855601,
        "first_name": "Drew",
        "type": "private"
      },
      "date": 1752928902,
      "text": "To help you with \"There's a problem mate\", I need to know which Samsung display model you're using.\n\n💡 **Quick identification**:\n• **WA series** (WA65C, WA75D, etc.) = Android displays\n• **WM series** (WM55B, WM75B, etc.) = Tizen displays\n\nWhich type do you have?",
      "reply_markup": {
        "inline_keyboard": [
          [
            {
              "text": "📱 Android (WA series)",
              "callback_data": "android_series"
            }
          ],
          [
            {
              "text": "💻 Tizen (WM series)",
              "callback_data": "tizen_series"
            }
          ],
          [
            {
              "text": "🔍 I know my model",
              "callback_data": "know_model"
            }
          ],
          [
            {
              "text": "📷 Photo of label",
              "callback_data": "photo_help"
            }
          ]
        ]
      }
    },
    "chat_instance": "-6208532454107160642",
    "data": "know_model"
  }
}
2025-07-19T13:42:00: 📨 Webhook received: 2025-07-19T12:42:00.415Z
2025-07-19T13:42:00: 📨 Request body: {
  "update_id": 706613116,
  "message": {
    "message_id": 1454,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752928920,
    "text": "Wa65c"
  }
}
2025-07-19T13:42:00: 🔍 Detected product: WA65C (Samsung)
2025-07-19T13:42:00: ✅ Updated user device tracking for 6863855601: WA65C
2025-07-19T13:42:05: 📨 Webhook received: 2025-07-19T12:42:05.662Z
2025-07-19T13:42:05: 📨 Request body: {
  "update_id": 706613117,
  "message": {
    "message_id": 1456,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752928925,
    "text": "Nice"
  }
}
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-19T12:42:06.517Z"}
2025-07-19T13:42:06: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-19T12:42:06.517Z"}
2025-07-19T13:46:54: 📨 Webhook received: 2025-07-19T12:46:54.028Z
2025-07-19T13:46:54: 📨 Request body: {
  "update_id": 706613118,
  "message": {
    "message_id": 1459,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752929213,
    "text": "Sure am"
  }
}
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-19T12:46:55.988Z"}
2025-07-19T13:46:55: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-19T12:46:55.988Z"}
2025-07-19T13:47:10: 📨 Webhook received: 2025-07-19T12:47:10.325Z
2025-07-19T13:47:10: 📨 Request body: {
  "update_id": 706613119,
  "message": {
    "message_id": 1462,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1752929230,
    "text": "How much is it?"
  }
}
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-19T12:47:11.535Z"}
2025-07-19T13:47:11: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-19T12:47:11.535Z"}
2025-07-19T17:56:52: 📨 Webhook received: 2025-07-19T16:56:52.362Z
2025-07-19T17:56:52: 📨 Request body: {
  "update_id": 706613120,
  "message": {
    "message_id": 1465,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1752944212,
    "text": "Help"
  }
}
{"level":"info","message":"🔄 Using Supabase fallback search","service":"weaviate-service","timestamp":"2025-07-19T16:56:54.088Z"}
2025-07-19T17:56:54: [32minfo[39m: 🔄 Using Supabase fallback search {"service":"weaviate-service","timestamp":"2025-07-19T16:56:54.088Z"}
2025-07-19T20:04:31: 🚀 AI Product Support Agent running on port 3000
2025-07-19T20:04:31: 🌐 Web interface: http://localhost:3000
2025-07-19T20:04:31: 🤖 Telegram bot: Active
2025-07-19T20:04:31: 📊 Database: Connected
2025-07-19T20:04:31: 🧠 OpenAI: Connected
2025-07-19T20:04:31: Database connection test failed: TypeError: fetch failed
2025-07-19T20:04:31: Tables may not exist yet - this is normal on first run
2025-07-19T20:04:31: ✅ Redis connected
2025-07-19T20:04:31: ✅ Redis connected
2025-07-19T20:04:31: 🔥 Warming up cache...
2025-07-19T20:04:31: 🔥 Warming up cache...
2025-07-19T20:04:31: ✅ Cache warmed up successfully
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-19T19:04:31.515Z"}
2025-07-19T20:04:31: ✅ Cache warmed up successfully
2025-07-19T20:04:31: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-19T19:04:31.515Z"}
2025-07-19T20:04:31: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-19T19:04:31.522Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-19T19:04:31.522Z"}
2025-07-19T20:04:31: Webhook set successfully
2025-07-19T20:04:31: Telegram bot webhook set up
2025-07-19T20:04:31: All services initialized successfully
{"level":"error","message":"❌ Failed to connect to Weaviate:","service":"weaviate-service","timestamp":"2025-07-19T19:04:31.671Z"}
2025-07-19T20:04:31: [31merror[39m: ❌ Failed to connect to Weaviate: {"service":"weaviate-service","timestamp":"2025-07-19T19:04:31.671Z"}
{"level":"warn","message":"⚠️ Weaviate not available, using Supabase only","service":"weaviate-service","timestamp":"2025-07-19T19:04:31.673Z"}
2025-07-19T20:04:31: [33mwarn[39m: ⚠️ Weaviate not available, using Supabase only {"service":"weaviate-service","timestamp":"2025-07-19T19:04:31.673Z"}
2025-07-19T20:04:31: [31merror[39m: ❌ Failed to connect to Weaviate: {"service":"weaviate-service","timestamp":"2025-07-19T19:04:31.677Z"}
2025-07-19T20:04:31: [33mwarn[39m: ⚠️ Weaviate not available, using Supabase only {"service":"weaviate-service","timestamp":"2025-07-19T19:04:31.677Z"}
2025-07-19T20:04:31: ✅ Telegram bot: Hybrid search initialized
{"level":"error","message":"❌ Failed to connect to Weaviate:","service":"weaviate-service","timestamp":"2025-07-19T19:04:31.677Z"}
{"level":"warn","message":"⚠️ Weaviate not available, using Supabase only","service":"weaviate-service","timestamp":"2025-07-19T19:04:31.677Z"}
Error: bind EADDRINUSE null:3000
    at listenOnPrimaryHandle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-07-20T21:36:01: Uncaught Exception: Error: bind EADDRINUSE null:3000
    at listenOnPrimaryHandle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21) {
  errno: -98,
  code: 'EADDRINUSE',
  syscall: 'bind',
  address: null,
  port: 3000
}
2025-07-20T21:36:01: ✅ Redis connected
2025-07-20T21:36:01: ✅ Redis connected
2025-07-20T21:36:01: 🔥 Warming up cache...
2025-07-20T21:36:01: 🔥 Warming up cache...
2025-07-20T21:36:01: ✅ Cache warmed up successfully
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-20T20:36:01.164Z"}
2025-07-20T21:36:01: ✅ Cache warmed up successfully
2025-07-20T21:36:01: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-20T20:36:01.164Z"}
2025-07-20T21:36:01: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-20T20:36:01.178Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-20T20:36:01.178Z"}
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-20T20:36:01.600Z"}
2025-07-20T21:36:01: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-20T20:36:01.600Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-20T20:36:01.602Z"}
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-20T20:36:01.614Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-20T20:36:01.616Z"}
2025-07-20T21:36:01: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-20T20:36:01.602Z"}
2025-07-20T21:36:01: ✅ Telegram bot: Hybrid search initialized
2025-07-20T21:36:01: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-20T20:36:01.614Z"}
2025-07-20T21:36:01: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-20T20:36:01.616Z"}
2025-07-20T21:36:02: Database connection successful
2025-07-20T21:36:02: Webhook set successfully
2025-07-20T21:36:02: Telegram bot webhook set up
2025-07-20T21:36:02: All services initialized successfully
2025-07-22T21:31:27: 🚀 AI Product Support Agent running on port 3000
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:31:27.173Z"}
2025-07-22T21:31:27: 🌐 Web interface: http://localhost:3000
2025-07-22T21:31:27: 🤖 Telegram bot: Active
2025-07-22T21:31:27: 📊 Database: Connected
2025-07-22T21:31:27: 🧠 OpenAI: Connected
2025-07-22T21:31:27: Database connection test failed: TypeError: fetch failed
2025-07-22T21:31:27: Tables may not exist yet - this is normal on first run
2025-07-22T21:31:27: ✅ Redis connected
2025-07-22T21:31:27: ✅ Redis connected
2025-07-22T21:31:27: 🔥 Warming up cache...
2025-07-22T21:31:27: 🔥 Warming up cache...
2025-07-22T21:31:27: ✅ Cache warmed up successfully
2025-07-22T21:31:27: ✅ Cache warmed up successfully
2025-07-22T21:31:27: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:31:27.173Z"}
2025-07-22T21:31:27: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:31:27.179Z"}
2025-07-22T21:31:27: [31merror[39m: ❌ Failed to connect to Weaviate: {"service":"weaviate-service","timestamp":"2025-07-22T20:31:27.185Z"}
2025-07-22T21:31:27: [33mwarn[39m: ⚠️ Weaviate not available, using Supabase only {"service":"weaviate-service","timestamp":"2025-07-22T20:31:27.185Z"}
2025-07-22T21:31:27: ✅ Telegram bot: Hybrid search initialized
2025-07-22T21:31:27: [31merror[39m: ❌ Failed to connect to Weaviate: {"service":"weaviate-service","timestamp":"2025-07-22T20:31:27.188Z"}
2025-07-22T21:31:27: [33mwarn[39m: ⚠️ Weaviate not available, using Supabase only {"service":"weaviate-service","timestamp":"2025-07-22T20:31:27.188Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:31:27.179Z"}
{"level":"error","message":"❌ Failed to connect to Weaviate:","service":"weaviate-service","timestamp":"2025-07-22T20:31:27.185Z"}
{"level":"warn","message":"⚠️ Weaviate not available, using Supabase only","service":"weaviate-service","timestamp":"2025-07-22T20:31:27.185Z"}
{"level":"error","message":"❌ Failed to connect to Weaviate:","service":"weaviate-service","timestamp":"2025-07-22T20:31:27.188Z"}
{"level":"warn","message":"⚠️ Weaviate not available, using Supabase only","service":"weaviate-service","timestamp":"2025-07-22T20:31:27.188Z"}
2025-07-22T21:31:27: Webhook set successfully
2025-07-22T21:31:27: Telegram bot webhook set up
2025-07-22T21:31:27: All services initialized successfully
2025-07-22T21:39:19: 
Shutting down gracefully...
Error: bind EADDRINUSE null:3001
    at listenOnPrimaryHandle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-07-22T21:39:22: Uncaught Exception: Error: bind EADDRINUSE null:3001
    at listenOnPrimaryHandle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21) {
  errno: -98,
  code: 'EADDRINUSE',
  syscall: 'bind',
  address: null,
  port: 3001
}
2025-07-22T21:39:22: ✅ Redis connected
2025-07-22T21:39:22: ✅ Redis connected
2025-07-22T21:39:22: 🔥 Warming up cache...
2025-07-22T21:39:22: 🔥 Warming up cache...
2025-07-22T21:39:22: ✅ Cache warmed up successfully
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:39:22.144Z"}
2025-07-22T21:39:22: ✅ Cache warmed up successfully
2025-07-22T21:39:22: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:39:22.144Z"}
2025-07-22T21:39:22: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:39:22.153Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:39:22.153Z"}
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:39:22.186Z"}
2025-07-22T21:39:22: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:39:22.186Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:39:22.188Z"}
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:39:22.191Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:39:22.193Z"}
2025-07-22T21:39:22: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:39:22.188Z"}
2025-07-22T21:39:22: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:39:22.191Z"}
2025-07-22T21:39:22: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:39:22.193Z"}
2025-07-22T21:39:22: ✅ Telegram bot: Hybrid search initialized
2025-07-22T21:39:22: Database connection successful
2025-07-22T21:39:22: Webhook set successfully
2025-07-22T21:39:22: Telegram bot webhook set up
2025-07-22T21:39:22: All services initialized successfully
2025-07-22T21:41:02: 
Shutting down gracefully...
2025-07-22T21:41:05: 🚀 AI Product Support Agent running on port 3003
2025-07-22T21:41:05: 🌐 Web interface: http://localhost:3003
2025-07-22T21:41:05: 🤖 Telegram bot: Active
2025-07-22T21:41:05: 📊 Database: Connected
2025-07-22T21:41:05: 🧠 OpenAI: Connected
2025-07-22T21:41:05: ✅ Redis connected
2025-07-22T21:41:05: ✅ Redis connected
2025-07-22T21:41:05: 🔥 Warming up cache...
2025-07-22T21:41:05: 🔥 Warming up cache...
2025-07-22T21:41:05: ✅ Cache warmed up successfully
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:41:05.066Z"}
2025-07-22T21:41:05: ✅ Cache warmed up successfully
2025-07-22T21:41:05: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:41:05.066Z"}
2025-07-22T21:41:05: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:41:05.078Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:41:05.078Z"}
2025-07-22T21:41:05: Database connection successful
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:41:05.170Z"}
2025-07-22T21:41:05: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:41:05.170Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:41:05.171Z"}
2025-07-22T21:41:05: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:41:05.171Z"}
2025-07-22T21:41:05: ✅ Telegram bot: Hybrid search initialized
2025-07-22T21:41:05: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:41:05.178Z"}
2025-07-22T21:41:05: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:41:05.179Z"}
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:41:05.178Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:41:05.179Z"}
2025-07-22T21:41:05: Webhook set successfully
2025-07-22T21:41:05: Telegram bot webhook set up
2025-07-22T21:41:05: All services initialized successfully
2025-07-22T21:42:58: 
Shutting down gracefully...
2025-07-22T21:43:00: 🚀 AI Product Support Agent running on port 3000
2025-07-22T21:43:00: 🌐 Web interface: http://localhost:3000
2025-07-22T21:43:00: 🤖 Telegram bot: Active
2025-07-22T21:43:00: 📊 Database: Connected
2025-07-22T21:43:00: 🧠 OpenAI: Connected
2025-07-22T21:43:00: ✅ Redis connected
2025-07-22T21:43:00: ✅ Redis connected
2025-07-22T21:43:00: 🔥 Warming up cache...
2025-07-22T21:43:00: 🔥 Warming up cache...
2025-07-22T21:43:01: ✅ Cache warmed up successfully
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:43:01.006Z"}
2025-07-22T21:43:01: ✅ Cache warmed up successfully
2025-07-22T21:43:01: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:43:01.006Z"}
2025-07-22T21:43:01: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:43:01.029Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:43:01.029Z"}
2025-07-22T21:43:01: Database connection successful
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:43:01.176Z"}
2025-07-22T21:43:01: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:43:01.176Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:43:01.179Z"}
2025-07-22T21:43:01: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:43:01.179Z"}
2025-07-22T21:43:01: ✅ Telegram bot: Hybrid search initialized
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:43:01.199Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:43:01.201Z"}
2025-07-22T21:43:01: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:43:01.199Z"}
2025-07-22T21:43:01: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:43:01.201Z"}
2025-07-22T21:43:01: Webhook set successfully
2025-07-22T21:43:01: Telegram bot webhook set up
2025-07-22T21:43:01: All services initialized successfully
2025-07-22T21:53:57: 
Shutting down gracefully...
2025-07-22T21:53:59: 🚀 AI Product Support Agent running on port 3000
2025-07-22T21:53:59: 🌐 Web interface: http://localhost:3000
2025-07-22T21:53:59: 🤖 Telegram bot: Active
2025-07-22T21:53:59: 📊 Database: Connected
2025-07-22T21:53:59: 🧠 OpenAI: Connected
2025-07-22T21:53:59: ✅ Redis connected
2025-07-22T21:53:59: ✅ Redis connected
2025-07-22T21:53:59: 🔥 Warming up cache...
2025-07-22T21:53:59: 🔥 Warming up cache...
2025-07-22T21:53:59: ✅ Cache warmed up successfully
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:53:59.662Z"}
2025-07-22T21:53:59: ✅ Cache warmed up successfully
2025-07-22T21:53:59: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:53:59.662Z"}
2025-07-22T21:53:59: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-22T20:53:59.671Z"}
{"level":"info","message":"✅ Cache service initialized","service":"weaviate-service","timestamp":"2025-07-22T20:53:59.671Z"}
2025-07-22T21:53:59: Database connection successful
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:53:59.770Z"}
2025-07-22T21:53:59: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:53:59.770Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:53:59.772Z"}
{"level":"info","message":"✅ Weaviate connection established","service":"weaviate-service","timestamp":"2025-07-22T20:53:59.777Z"}
{"level":"info","message":"✅ Hybrid search initialized with Weaviate","service":"weaviate-service","timestamp":"2025-07-22T20:53:59.778Z"}
2025-07-22T21:53:59: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:53:59.772Z"}
2025-07-22T21:53:59: ✅ Telegram bot: Hybrid search initialized
2025-07-22T21:53:59: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-22T20:53:59.777Z"}
2025-07-22T21:53:59: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-22T20:53:59.778Z"}
2025-07-22T21:53:59: Webhook set successfully
2025-07-22T21:53:59: Telegram bot webhook set up
2025-07-22T21:53:59: All services initialized successfully
2025-07-22T21:53:59: 📨 Webhook received: 2025-07-22T20:53:59.954Z
2025-07-22T21:53:59: 📨 Request body: {
  "update_id": 706613128,
  "message": {
    "message_id": 1474,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753186437,
    "text": "Hello"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.027Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613129,
  "message": {
    "message_id": 1475,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753208311,
    "text": "How are you?"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.078Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613130,
  "message": {
    "message_id": 1476,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753216072,
    "text": "Hi"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.102Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613131,
  "message": {
    "message_id": 1477,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753216482,
    "text": "Hello"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.147Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613122,
  "message": {
    "message_id": 1468,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1753186221,
    "text": "Can you help"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.159Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613132,
  "message": {
    "message_id": 1478,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753216566,
    "text": "Hello"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.197Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613123,
  "message": {
    "message_id": 1469,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1753186240,
    "text": "Hello"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.263Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613124,
  "message": {
    "message_id": 1470,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1753186393,
    "text": "Hi"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.289Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613133,
  "message": {
    "message_id": 1479,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753217003,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-22T21:54:00: 🗑️ Clearing cache pattern: /start
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.388Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613125,
  "message": {
    "message_id": 1471,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1753186403,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-22T21:54:00: 🗑️ Clearing cache pattern: /start
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.486Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613134,
  "message": {
    "message_id": 1480,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753217202,
    "text": "Hello"
  }
}
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.508Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613126,
  "message": {
    "message_id": 1472,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1753186406,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-22T21:54:00: 🗑️ Clearing cache pattern: /start
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.520Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613135,
  "message": {
    "message_id": 1481,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1753217219,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-22T21:54:00: 🗑️ Clearing cache pattern: /start
2025-07-22T21:54:00: 📨 Webhook received: 2025-07-22T20:54:00.588Z
2025-07-22T21:54:00: 📨 Request body: {
  "update_id": 706613127,
  "message": {
    "message_id": 1473,
    "from": {
      "id": 6863855601,
      "is_bot": false,
      "first_name": "Drew",
      "language_code": "en"
    },
    "chat": {
      "id": 6863855601,
      "first_name": "Drew",
      "type": "private"
    },
    "date": 1753186407,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-22T21:54:00: 🗑️ Clearing cache pattern: /start
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Hello\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.547Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Hello" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.547Z"}
2025-07-22T21:54:02: 💾 Cached search results for: "Hello"
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Hello\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.661Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Hello" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.661Z"}
2025-07-22T21:54:02: 💾 Cached search results for: "Hello"
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"How are you?\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.736Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "How are you?" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.736Z"}
2025-07-22T21:54:02: 💾 Cached search results for: "How are you?"
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Hello\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.747Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Hello" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.747Z"}
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Hi\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.758Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Hi" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.758Z"}
2025-07-22T21:54:02: 💾 Cached search results for: "Hello"
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
2025-07-22T21:54:02: 💾 Cached search results for: "Hi"
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Can you help\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.829Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Can you help" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.829Z"}
2025-07-22T21:54:02: 💾 Cached search results for: "Can you help"
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Hello\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.856Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Hello" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.856Z"}
2025-07-22T21:54:02: 💾 Cached search results for: "Hello"
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Hi\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:02.925Z"}
2025-07-22T21:54:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Hi" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:02.925Z"}
2025-07-22T21:54:02: 💾 Cached search results for: "Hi"
2025-07-22T21:54:02: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:02: 📚 After filtering: 1 relevant documents
{"level":"info","message":"✅ Weaviate search: 3 KB + 1 docs + 0 structured for \"Hello\"","service":"weaviate-service","timestamp":"2025-07-22T20:54:06.629Z"}
2025-07-22T21:54:06: [32minfo[39m: ✅ Weaviate search: 3 KB + 1 docs + 0 structured for "Hello" {"service":"weaviate-service","timestamp":"2025-07-22T20:54:06.629Z"}
2025-07-22T21:54:06: 💾 Cached search results for: "Hello"
2025-07-22T21:54:06: 📚 sendDocumentDownloads: Processing 1 documents
2025-07-22T21:54:06: 📚 After filtering: 1 relevant documents
2025-07-22T21:54:21: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:21: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:21: File serve error: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:27: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:27: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:27: File serve error: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:40: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:40: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-22T21:54:40: File serve error: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
