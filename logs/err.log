Error: bind EADDRINUSE null:3000
    at listenOnPrimary<PERSON>andle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-07-20T21:36:01: Uncaught Exception: Error: bind EADDRINUSE null:3000
    at listenOnPrimaryHandle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21) {
  errno: -98,
  code: 'EADDRINUSE',
  syscall: 'bind',
  address: null,
  port: 3000
}
2025-07-22T21:39:19: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1795:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:278:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:293:13)
    at process.emit (node:events:518:28)
Error: bind EADDRINUSE null:3001
    at listenOnPrimaryHandle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-07-22T21:39:22: Uncaught Exception: Error: bind EADDRINUSE null:3001
    at listenOnPrimaryHandle (node:net:2021:18)
    at rr (node:internal/cluster/child:163:12)
    at Worker.<anonymous> (node:internal/cluster/child:113:7)
    at process.onInternalMessage (node:internal/cluster/utils:49:5)
    at process.emit (node:events:530:35)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21) {
  errno: -98,
  code: 'EADDRINUSE',
  syscall: 'bind',
  address: null,
  port: 3001
}
2025-07-22T21:41:02: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1795:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:278:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:293:13)
    at process.emit (node:events:518:28)
2025-07-22T21:42:58: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1795:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:278:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:293:13)
    at process.emit (node:events:518:28)
