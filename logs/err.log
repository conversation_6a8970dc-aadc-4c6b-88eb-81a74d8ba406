2025-07-06T02:22:36: Error stopping bot: Error: <PERSON><PERSON> is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:242:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T02:27:29: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T02:32:14: Error stopping bot: Error: <PERSON><PERSON> is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:242:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T02:38:25: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:242:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T02:47:43: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:245:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T02:54:14: Error creating session: {
  code: '23502',
  details: 'Failing row contains (674b282d-1b94-42ac-b76c-e2f6174468e3, null, web, {}, 2025-07-06 01:54:14.049761+00, 2025-07-06 01:54:14.049761+00).',
  hint: null,
  message: 'null value in column "user_id" of relation "chat_sessions" violates not-null constraint'
}
2025-07-06T02:54:14: Error creating new session: {
  code: '23502',
  details: 'Failing row contains (674b282d-1b94-42ac-b76c-e2f6174468e3, null, web, {}, 2025-07-06 01:54:14.049761+00, 2025-07-06 01:54:14.049761+00).',
  hint: null,
  message: 'null value in column "user_id" of relation "chat_sessions" violates not-null constraint'
}
2025-07-06T02:54:14: Clear chat error: {
  code: '23502',
  details: 'Failing row contains (674b282d-1b94-42ac-b76c-e2f6174468e3, null, web, {}, 2025-07-06 01:54:14.049761+00, 2025-07-06 01:54:14.049761+00).',
  hint: null,
  message: 'null value in column "user_id" of relation "chat_sessions" violates not-null constraint'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T02:56:42: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:256:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T02:57:58: Error fetching session: {
  code: 'PGRST002',
  details: null,
  hint: null,
  message: 'Could not query the database for the schema cache. Retrying.'
}
2025-07-06T02:57:58: Error in getOrCreateSession: {
  code: 'PGRST002',
  details: null,
  hint: null,
  message: 'Could not query the database for the schema cache. Retrying.'
}
2025-07-06T02:57:58: Session initialization error: {
  code: 'PGRST002',
  details: null,
  hint: null,
  message: 'Could not query the database for the schema cache. Retrying.'
}
2025-07-06T02:58:06: Error fetching session: {
  code: 'PGRST002',
  details: null,
  hint: null,
  message: 'Could not query the database for the schema cache. Retrying.'
}
2025-07-06T02:58:06: Error in getOrCreateSession: {
  code: 'PGRST002',
  details: null,
  hint: null,
  message: 'Could not query the database for the schema cache. Retrying.'
}
2025-07-06T02:58:06: Session initialization error: {
  code: 'PGRST002',
  details: null,
  hint: null,
  message: 'Could not query the database for the schema cache. Retrying.'
}
2025-07-06T03:02:14: Knowledge base search error: {
  code: 'PGRST100',
  details: `unexpected "'" expecting letter, digit, "-", "->>", "->" or delimiter (.)`,
  hint: null,
  message: `"failed to parse logic tree ((title.ilike.%That's all fine, I've noticed there is a lock icon showing but still can't unlock it%, content.ilike.%That's all fine, I've noticed there is a lock icon showing but still can't unlock it%))" (line 1, column 35)`
}
2025-07-06T03:02:14: Document search error: {
  code: 'PGRST100',
  details: `unexpected "'" expecting letter, digit, "-", "->>", "->" or delimiter (.)`,
  hint: null,
  message: `"failed to parse logic tree ((original_name.ilike.%That's all fine, I've noticed there is a lock icon showing but still can't unlock it%, content.ilike.%That's all fine, I've noticed there is a lock icon showing but still can't unlock it%))" (line 1, column 43)`
}
2025-07-06T03:11:51: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:301:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T03:31:15: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:301:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T03:33:48: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T03:35:42: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T03:42:08: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T03:44:02: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T03:45:11: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T03:46:39: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T03:54:59: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T03:56:17: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:301:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T04:03:33: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:410:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T04:13:17: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:410:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
2025-07-06T04:23:49: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:24:58: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:32:09: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T04:35:54: Error creating session: {
  code: '23502',
  details: 'Failing row contains (6884ffb5-fd1c-4630-ba8a-97e0548108a2, null, web, {}, 2025-07-06 03:35:54.915865+00, 2025-07-06 03:35:54.915865+00).',
  hint: null,
  message: 'null value in column "user_id" of relation "chat_sessions" violates not-null constraint'
}
2025-07-06T04:35:54: Error creating new session: {
  code: '23502',
  details: 'Failing row contains (6884ffb5-fd1c-4630-ba8a-97e0548108a2, null, web, {}, 2025-07-06 03:35:54.915865+00, 2025-07-06 03:35:54.915865+00).',
  hint: null,
  message: 'null value in column "user_id" of relation "chat_sessions" violates not-null constraint'
}
2025-07-06T04:35:54: Clear chat error: {
  code: '23502',
  details: 'Failing row contains (6884ffb5-fd1c-4630-ba8a-97e0548108a2, null, web, {}, 2025-07-06 03:35:54.915865+00, 2025-07-06 03:35:54.915865+00).',
  hint: null,
  message: 'null value in column "user_id" of relation "chat_sessions" violates not-null constraint'
}
2025-07-06T04:37:16: Knowledge base search error: {
  code: 'PGRST100',
  details: 'unexpected "%" expecting letter, digit, "-", "->>", "->" or delimiter (.)',
  hint: null,
  message: `"failed to parse logic tree ((title.ilike.%When I touch the screen it''s unresponsive, remote control not doing anything%, content.ilike.%When I touch the screen it''s unresponsive, remote control not doing anything%))" (line 1, column 94)`
}
2025-07-06T04:37:16: Document search error: {
  code: 'PGRST100',
  details: 'unexpected "%" expecting letter, digit, "-", "->>", "->" or delimiter (.)',
  hint: null,
  message: `"failed to parse logic tree ((original_name.ilike.%When I touch the screen it''s unresponsive, remote control not doing anything%, content.ilike.%When I touch the screen it''s unresponsive, remote control not doing anything%))" (line 1, column 102)`
}
2025-07-06T04:38:43: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1222dce70035-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:43 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=w8CilhRmZjX9QKjPhy_7tgC1J3yD0P2lFsNK090eRng-1751773123-*******-q2ILUo_RZ0qX38rPKw8vraPt5LVxX4s.X4ZxHUFG9amxOo.R0DsN6L.NJEMXt373rkrCULIEa7pyJRMIDbGmixv6qXdCgR_CgOBJcRqb920; path=/; expires=Sun, 06-Jul-25 04:08:43 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=jmrkRXIq5xl56BnKTu__EvLRekCxjTYgojeeuIYP8_E-1751773123800-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_1344aaf2e805737e9f1f8016b214ea82'
  },
  request_id: 'req_1344aaf2e805737e9f1f8016b214ea82',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:45: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac12321ee594b7-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=j9tpVeJgFt5Xtz5Bml4sdn8J8GyHMSH_oiZznzV78no-1751773125-*******-eLprA4gqGmTq_gwkzRy3q5qOFv4sT7I1gqRRrACnvJGbtyctMUWBoClJ4pxcnRNv2c77_9u30RBHGyNOSJgv06XZahBagTTeludx_8aOTP0; path=/; expires=Sun, 06-Jul-25 04:08:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=zW4DSMhVjbtrSYvtlztkvHR0XufaStnRYh5ZdS7j4mM-1751773125560-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_f2458987d23404d2533b7fe3ba9d2071'
  },
  request_id: 'req_f2458987d23404d2533b7fe3ba9d2071',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:45: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac12333f3d94b7-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=zrzNE4rS02k16cVDG9HVf_KPR9kp_R8d2tR43Awn9lc-1751773125-*******-wi7kAexRHStVY2DAi_5lc1J0v21s3PJn6cjtp_gOiuXV0VkK0D9YHfvAErqyB.wWHbak4U.jgFlKorHjkG0oBDmaGzxTHMjxRfOst7xDwsw; path=/; expires=Sun, 06-Jul-25 04:08:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=Cv8xfEKBSBB.0aXtIuOm9rJEh2NU2swk1FLgfb4IKGs-1751773125746-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_016936de11d5f9c115c29b510d698cbe'
  },
  request_id: 'req_016936de11d5f9c115c29b510d698cbe',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:47: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac123f1ac394b7-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:47 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=lDaMue_oLBrrlovkNJNzxzWRj65cYPU9EGIVhHqrn0U-1751773127-*******-VEEk3oYWejYSJ9Tzuh225oMB2ShfWADNHQkq9Xk9bP5CcDhfhTLqwOwJhC3ZQYh01gRfIBirJgpbR6Zl.afFpcvNHCZOvDu7ZgykFr6O5tg; path=/; expires=Sun, 06-Jul-25 04:08:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=0aHqhx3IsMFIoCGWAy9qhB4mPU1tPT5oNefQjkmjpok-1751773127716-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_088397bc08a7a356f91d9a5f66b4fe1a'
  },
  request_id: 'req_088397bc08a7a356f91d9a5f66b4fe1a',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:47: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac123f1ac394b7-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:47 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=lDaMue_oLBrrlovkNJNzxzWRj65cYPU9EGIVhHqrn0U-1751773127-*******-VEEk3oYWejYSJ9Tzuh225oMB2ShfWADNHQkq9Xk9bP5CcDhfhTLqwOwJhC3ZQYh01gRfIBirJgpbR6Zl.afFpcvNHCZOvDu7ZgykFr6O5tg; path=/; expires=Sun, 06-Jul-25 04:08:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=0aHqhx3IsMFIoCGWAy9qhB4mPU1tPT5oNefQjkmjpok-1751773127716-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_088397bc08a7a356f91d9a5f66b4fe1a'
  },
  request_id: 'req_088397bc08a7a356f91d9a5f66b4fe1a',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:55: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac126f9b24773b-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:55 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=XCe8YuO2qzzFQb_bYmLd5CUUxLpks9IEfJqmRbQwM74-1751773135-*******-spTmpb6XRl51TMYz7zLBv9VUJUhDjJsYXBI2G4YRjgFA0Lxsy_lmz_GOK1zfr9LvWGD0mp2ep_FlhFs2oa_v8LfTbTyQWcuVwzpatvU6urs; path=/; expires=Sun, 06-Jul-25 04:08:55 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=XrNS3lzLE0BxY4xaV3f8oiqFmlBfXM2zXZT9KQpWDGA-1751773135446-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_84c68be2933c312d3eeb1d8385f4f714'
  },
  request_id: 'req_84c68be2933c312d3eeb1d8385f4f714',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:57: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac127a4c22b2e5-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:57 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=IPwjXhx5Ulxqvq2E6_yDRPnjVzSRYK23MgVOaWZt8w4-1751773137-*******-HeaOHva393CgD9fnYoMK1tdeDArHy509E849RfNragX2dq3whcIeTrYLLtp_Pj2gs8gyKj.7g7JTCF2zRHrQDnNdY_H8KXhRqUS1as46ym4; path=/; expires=Sun, 06-Jul-25 04:08:57 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=1a6qq7jjCmoanl3Nrxx723BLwtlvck0x9C.7z7e54FY-1751773137109-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_609ce39bc837f2f6b5aab751975ce0a3'
  },
  request_id: 'req_609ce39bc837f2f6b5aab751975ce0a3',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:57: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac127abcb8773b-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:57 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=nAkSBPC7tNm_NzSO5_E2GYilnqRQvIYiRTuTS4J5SDY-1751773137-*******-wOHON8DwnJYmnuO4M2cQHFiOOClMCA_lD11_RpkzTaEV7Q.uzUYZlU70xD3CV6hjb3Vsn4luyylKCSdOvPFbrn3JQK6rZLUKhq8bq4RZLQ4; path=/; expires=Sun, 06-Jul-25 04:08:57 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=8f1Yr7hVwBdIsqi0RR8jPWubYw9iNbI4JVHar5amWgk-1751773137174-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_5f3fdbbc11e09894eae45e2f82d1a2f7'
  },
  request_id: 'req_5f3fdbbc11e09894eae45e2f82d1a2f7',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:59: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac12881ef2773b-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:59 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=_2HHLJ_pNo.a0GWrc7UF6d4Lte0NZuyqstohv8.bmMI-1751773139-*******-z8WaTBcPSlObgZsx1gkH4gGkK5FdO05ifg27u3MzU8dpCMk_u1q8_Vi8pHQhqEAXmE8Dex.qLfObxb_bRQ8YXlinbyS0Wj5nnyqccs1cmp4; path=/; expires=Sun, 06-Jul-25 04:08:59 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=Y8E7FZP525_AJSOvFvom8dKhiGrJ80WjCAc5QCLsqT0-1751773139452-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_bb172e9e6ae3270bceb1b570aab79e5d'
  },
  request_id: 'req_bb172e9e6ae3270bceb1b570aab79e5d',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:38:59: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac12881ef2773b-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:38:59 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=_2HHLJ_pNo.a0GWrc7UF6d4Lte0NZuyqstohv8.bmMI-1751773139-*******-z8WaTBcPSlObgZsx1gkH4gGkK5FdO05ifg27u3MzU8dpCMk_u1q8_Vi8pHQhqEAXmE8Dex.qLfObxb_bRQ8YXlinbyS0Wj5nnyqccs1cmp4; path=/; expires=Sun, 06-Jul-25 04:08:59 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=Y8E7FZP525_AJSOvFvom8dKhiGrJ80WjCAc5QCLsqT0-1751773139452-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_bb172e9e6ae3270bceb1b570aab79e5d'
  },
  request_id: 'req_bb172e9e6ae3270bceb1b570aab79e5d',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:39:10: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:39:55: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac13e5f85f773d-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:39:55 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=pu962YF5aYvFZNSW740w_WhD1RzYogcgbr6MXmSLBho-1751773195-*******-HaKqDUYHPbnhWlKNciljZEsoAVdr7U98aMu.SRgGBQkRMRxSlh_nnOXMKUdiJQ0q6nHOUd5fCdYH8AlSvlMQtf9H.Wiq32adYDcz3vPT7WY; path=/; expires=Sun, 06-Jul-25 04:09:55 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=TxPSKSB7krMrsMtfKyY3mP1oIg1sFAv6CR_0LhfOfRg-1751773195361-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_88ccff8f379bef9bf91f0fe1c822a256'
  },
  request_id: 'req_88ccff8f379bef9bf91f0fe1c822a256',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:39:57: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac13f179f0773d-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:39:57 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=bxzjf2C5tCZ8OnXROwgd91JuUm1wcUm871PdxfBSDo8-1751773197-*******-b9uDQcmn45mFCin3EezErHIQ6mQ80Sf57C8qx8BiGV8I00_K0Vvktra2vGldksbe0j1VQ5TWxG.R0vXOgbbcQb.nRFQQ9aIEGWhmdxQJiBw; path=/; expires=Sun, 06-Jul-25 04:09:57 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=fo3kC5jKTLXmuzyxI0bSXJZ54VdMN1IGLqhu6yu8pw4-1751773197150-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_6c792d97c693bb43a5735dfdba9b2107'
  },
  request_id: 'req_6c792d97c693bb43a5735dfdba9b2107',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:39:57: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac13f1dd645588-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:39:57 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=nv8FAh5bnvKSjn99vAql3ZiUbVHfn.O2WVPd7rw_gMw-1751773197-*******-eyhtHwgLVyl0hrkgfXC9fZdN1JP1bRA0uJysNpA7o8C.4BO4ByrEvaNn9dCZvhzccp.I49BsijbBCXZzxVyjExCQB_KegJNmf_SP5l_EP1M; path=/; expires=Sun, 06-Jul-25 04:09:57 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=aP49R.MeJSy.ANd2xOSFmq3wRWr_9VGXNqlpxGTSI.E-1751773197193-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_83bcdca3337e7c0aa37c5e1f4da272a5'
  },
  request_id: 'req_83bcdca3337e7c0aa37c5e1f4da272a5',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:39:59: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac13fd2c835588-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:39:59 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=yiSO00DaC016mw1FIBkYZ_L4tP00CCSMjkZqs02PWW8-1751773199-*******-IzLtbkhRYBPfUSI2yLfbv__tfxgtPS6i5fwoiZamMTau_enQF7g4N1NFg6zHkrN35Vhsrgb4F65O4V0malraAMaDx.DamUSl7ZLbyQxwpCA; path=/; expires=Sun, 06-Jul-25 04:09:59 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=NZjD9O78W5NB28vwR1uoAmOs094XmjCzpN2mW0Slf9E-1751773199067-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_564bf290b04473af77570a935e42405c'
  },
  request_id: 'req_564bf290b04473af77570a935e42405c',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:39:59: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac13fd2c835588-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:39:59 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=yiSO00DaC016mw1FIBkYZ_L4tP00CCSMjkZqs02PWW8-1751773199-*******-IzLtbkhRYBPfUSI2yLfbv__tfxgtPS6i5fwoiZamMTau_enQF7g4N1NFg6zHkrN35Vhsrgb4F65O4V0malraAMaDx.DamUSl7ZLbyQxwpCA; path=/; expires=Sun, 06-Jul-25 04:09:59 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=NZjD9O78W5NB28vwR1uoAmOs094XmjCzpN2mW0Slf9E-1751773199067-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_564bf290b04473af77570a935e42405c'
  },
  request_id: 'req_564bf290b04473af77570a935e42405c',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:20: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1481ec523784-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:20 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=Gg.****************************************-1751773220-*******-C5YMu48HhHbi9d7L4bA1_FYrIuKMJT1Cl9ezd64xsMLYoSPheloGaDg14wGDpAU_5yrdw.Qz_rK.M7YTFpX1Q1.fKhgrtby1lxC5Pqi6IqY; path=/; expires=Sun, 06-Jul-25 04:10:20 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=XZ99V5r6qxEljO5f4ynPLHOiJgAyn8jU7fO0Qw59Jts-1751773220343-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_ff9240fa7207b1e05945114da8b775dd'
  },
  request_id: 'req_ff9240fa7207b1e05945114da8b775dd',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:22: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac148cffcee8ff-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:22 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=wOXJfx4AQdl6mSLHYAto1AZJcxidHvphSDlh3OXwUCU-1751773222-*******-XrLbL4sVf_Sr93XhBSeLUpYtx6gS3QoGbPlO8xlZip2eopiR_UXhr4QPoZakLtOObd4atjPD.jHcVeLvb7K4uF.e8yOHYLuSNX_SX14X.ZE; path=/; expires=Sun, 06-Jul-25 04:10:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=xYJ2ePVT_tnc2Pkbo8Q.zv.a8tytPpwafQy9htM6D_I-1751773222030-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_002ed1979548986d8bfcbe591a16e0fa'
  },
  request_id: 'req_002ed1979548986d8bfcbe591a16e0fa',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:22: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac148d1c893784-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:22 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=XfPpM1BhbPM0DeXKXuaxQPFLB5ZAXIQa8L_uCcFqW1o-1751773222-*******-nfSWpd3Tz.vAx_exwKRG1pZcDihbIxMJaO7v9bCA7u4QQ3je9jqJkuecw_QVhD4m_7wKme0rLt82kV0vQiJSdsMMjCtQzrtedidx6a2B1hA; path=/; expires=Sun, 06-Jul-25 04:10:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=heXf9Zo5A8YZe73FiKpDPYHknL3ws4UDG854re80x34-1751773222042-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_91760542ab8ba6866f5892986abc982d'
  },
  request_id: 'req_91760542ab8ba6866f5892986abc982d',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:23: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:40:24: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac149b4f443784-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:24 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=XPr9j4g6qBGgUqdFQ5S8ZymCwQwNqD68M0TGwCjq_yc-1751773224-*******-YnsRf_BbuFuBzUoau_M1.ZrNmO.JIjfa.GkalowYONp1rilSWs210J14.Knhh6PN0fFde1TVqeYK8H7LQGM0LMCpvO22jJgk.07srEWDjz8; path=/; expires=Sun, 06-Jul-25 04:10:24 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=AvXeG7uJAJJZiWfra_Wh10cIX3uqiZ0iJc4mTly_Q4E-1751773224383-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_e25f1390923bbd2233c2ecfa61c6a91e'
  },
  request_id: 'req_e25f1390923bbd2233c2ecfa61c6a91e',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:24: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac149b4f443784-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:24 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=XPr9j4g6qBGgUqdFQ5S8ZymCwQwNqD68M0TGwCjq_yc-1751773224-*******-YnsRf_BbuFuBzUoau_M1.ZrNmO.JIjfa.GkalowYONp1rilSWs210J14.Knhh6PN0fFde1TVqeYK8H7LQGM0LMCpvO22jJgk.07srEWDjz8; path=/; expires=Sun, 06-Jul-25 04:10:24 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=AvXeG7uJAJJZiWfra_Wh10cIX3uqiZ0iJc4mTly_Q4E-1751773224383-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_e25f1390923bbd2233c2ecfa61c6a91e'
  },
  request_id: 'req_e25f1390923bbd2233c2ecfa61c6a91e',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:43: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac15150d6bcdc1-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:43 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=vWx6S1wggwJXt8FmfQthQAQUxYCVgTaQYLTNBnvJ2Lo-1751773243-*******-XqjQ0yfkLdMCy.9oWOhvHv8TSq90AClLgmVBXG3qjAprFEE5BkipmBumhSa3o45GMR1drTKXG_7wxYDD4w1AXWgqy5NFCvqk7jmRrx9gYkM; path=/; expires=Sun, 06-Jul-25 04:10:43 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=UtfZR8VnuKbo8Rf_qldSz8DJxYK.G.4ecR2D2gd52.s-1751773243851-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_f9d981158ad386a69c828134367e0846'
  },
  request_id: 'req_f9d981158ad386a69c828134367e0846',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:45: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac151f9aef49c9-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=BxVkTuXlh6txG64nYMirX_7z1ti_2.WKLJQGNJH6lRw-1751773245-*******-itm1RJ.z6Hq5qDfBOE1BtpEA_Jm0RdsHEL5U_Xooi2vF7oDqjyerUT_e3wdnUrUSmKJ7k8wINAubr.MAXzOleYrWg.SYZfYl4wR03Sid3C0; path=/; expires=Sun, 06-Jul-25 04:10:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=Qc_BWDvrAciAKlUI8CsJc.iohMq9BTarC8O3SnrGGyw-1751773245479-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_7879f133a19a20a40ced8155747fd219'
  },
  request_id: 'req_7879f133a19a20a40ced8155747fd219',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:45: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac151f787fcdc1-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=CW8SmMfRcmBW34QDvhCnaTLxi7AKtNGCAMWozQbVJZM-1751773245-*******-Sv7Adb5ISYKqM1DBz_mn_zhz8swDg2m79sRhGpG.L3fIXeHjYFQYR8jZPLwAjXM8ZiamVI4ehrtB7D.hceFq4X3xWO4PU8jgg7BdiT65J0c; path=/; expires=Sun, 06-Jul-25 04:10:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=fD2Xfux_t0KR2d6HDbQFdCTOOP4r94ECbeKCPOEGiwc-1751773245480-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_947b63bc31073192a3eb3ec2d6147f6c'
  },
  request_id: 'req_947b63bc31073192a3eb3ec2d6147f6c',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:47: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac152b8c8dcdc1-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:47 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=dILnD5FnxtsvH_ydZZMZsaDsXBhTo8aK2czDsU6cIck-1751773247-*******-.2n5Fr_7j2Hei9pwdULxJGwpcMGYuugD1uXjRFb2Gk4FYw2R38yJIgbGLqfS0xgReZzh8GkH66PXnOUX_Re96zspYIXrrVc9ZViLyHN7pZw; path=/; expires=Sun, 06-Jul-25 04:10:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=u6OoXJgxSBbmhHNi.hascjuPK_wzIF7dwot7WEWxMUg-1751773247450-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_5db0c097095d9a0d70b546d750da975f'
  },
  request_id: 'req_5db0c097095d9a0d70b546d750da975f',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:40:47: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac152b8c8dcdc1-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:40:47 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=dILnD5FnxtsvH_ydZZMZsaDsXBhTo8aK2czDsU6cIck-1751773247-*******-.2n5Fr_7j2Hei9pwdULxJGwpcMGYuugD1uXjRFb2Gk4FYw2R38yJIgbGLqfS0xgReZzh8GkH66PXnOUX_Re96zspYIXrrVc9ZViLyHN7pZw; path=/; expires=Sun, 06-Jul-25 04:10:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=u6OoXJgxSBbmhHNi.hascjuPK_wzIF7dwot7WEWxMUg-1751773247450-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_5db0c097095d9a0d70b546d750da975f'
  },
  request_id: 'req_5db0c097095d9a0d70b546d750da975f',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:41:44: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac168d5bb03c8b-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:41:44 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=LhAU97qOAYkcKNBb0zkxo.rHSJ47TUQshB6vnV5zV5Y-1751773304-*******-ReGmrTtzaIOuKqCTV_CVNe7hamE6rVQmracxT1ofCFP3SalYaroHgTBffeb5ccTp8vWPctjz.6j_q9fznGFytmhjM6FeZ7tMmZOcqniVet4; path=/; expires=Sun, 06-Jul-25 04:11:44 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=PgDnOCmBv2p6ViOef8UydWzyxqbCTs0_eUudyvkZQtQ-1751773304075-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_1c7f6060371c71ce96bdce830058054f'
  },
  request_id: 'req_1c7f6060371c71ce96bdce830058054f',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:41:45: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1697db2b3c8b-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:41:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=2Up5v73mfQDIlixdXi.VKdslOWXZ6kYUmiPqpgWfih4-1751773305-*******-Oyd9L4w0T2xY0D8wYw9F3CNfBD7L19Gf_sZQTezPFyWok_wFXcxx7RE84RIg2nRc2d.pxTbOA4.8jO2dhbPxA8n2EVEbUc.sWRlrym8Tn08; path=/; expires=Sun, 06-Jul-25 04:11:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=2IhG1GCJRtCR1TltHaJMHVz1bR_46zd9WYDJIOgiRlc-1751773305677-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_b09595b25e3f158851a3ef69a3b59725'
  },
  request_id: 'req_b09595b25e3f158851a3ef69a3b59725',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:41:45: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac16987c814052-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:41:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=NCuYQCzbPDMI6UETuHtR8sFQ2My6onP_dTq.TOU4QQQ-1751773305-*******-jv_5OaCz7ui8Wwj7B5i.Ktw8Kg5TEa8a15zYaRGQ2fGh7Go2_1BRZPSDB6U2CpE4PCLaM3fanESoZ44iGCtTIvYc.VaKCQnQHZwJh36.IM0; path=/; expires=Sun, 06-Jul-25 04:11:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=zIXbusKERA4N0TrwZoZNBachJkqU8w0EI6QvRh8.AhU-1751773305781-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_01c809f994b3e7d0e20f42bed3fc6fe9'
  },
  request_id: 'req_01c809f994b3e7d0e20f42bed3fc6fe9',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:41:47: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac16a3dc4d4052-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:41:47 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=SyRQapB471GLT9400nVBruZlfbBwR3E9k3ASCxIrhz4-1751773307-*******-y.HWLleZyNLn4GGZ_2Uf4Gl8Fxv1ttn0gvlpJkdJ3hirlQ4FZiwIGeuAOGqda1LXyJ0kfH0GDsuW5ge9lFvknOt8dJowydxHpjUDjNXU4Gw; path=/; expires=Sun, 06-Jul-25 04:11:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=GoRMx12OSHb9rH5n_oqBjPCZDbGYnfoCFUzyxpMPs4g-1751773307682-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_86d3c1368cd71ad58d28b509a4447de0'
  },
  request_id: 'req_86d3c1368cd71ad58d28b509a4447de0',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:41:47: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac16a3dc4d4052-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:41:47 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=SyRQapB471GLT9400nVBruZlfbBwR3E9k3ASCxIrhz4-1751773307-*******-y.HWLleZyNLn4GGZ_2Uf4Gl8Fxv1ttn0gvlpJkdJ3hirlQ4FZiwIGeuAOGqda1LXyJ0kfH0GDsuW5ge9lFvknOt8dJowydxHpjUDjNXU4Gw; path=/; expires=Sun, 06-Jul-25 04:11:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=GoRMx12OSHb9rH5n_oqBjPCZDbGYnfoCFUzyxpMPs4g-1751773307682-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_86d3c1368cd71ad58d28b509a4447de0'
  },
  request_id: 'req_86d3c1368cd71ad58d28b509a4447de0',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:42:12: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:42:19: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac17681f846518-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:42:19 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=q0rjSy7Ao7KH1z6Jo3F28df9Rp6l8x180O_xgnVF8bQ-1751773339-*******-T_Mhut7nk.uLtBeS2uME48Ff8aqUujWSOam5aWjQK7Od2T9HMgLeRYKFjvR_HKlqCf_memyUrdXVHhUBPjbsiXUorYpVAuIN0.5GxsC6kko; path=/; expires=Sun, 06-Jul-25 04:12:19 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=pXazuNTuM9TLwbDz96AxI6H6GAGaEVLzvL1c1wQFVaE-1751773339041-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_708ba9ebd611def99a0cbb17e0bb7908'
  },
  request_id: 'req_708ba9ebd611def99a0cbb17e0bb7908',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:42:20: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1773aa306518-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:42:20 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=ykfbQi9KK2KblQcHrAJ1PjiZpdEiE8_z7OwUU2PIYxI-1751773340-*******-IlOz44v96WbvK1wShUDwcKYUMVFe4JJq76rnPK8xRfYGBZZQy1zX0gQN6tqZ1K.kUv856rsr2sl4nT52GPqtG33bwbWqpWHubR2jE57f950; path=/; expires=Sun, 06-Jul-25 04:12:20 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=O0YgFuzI5N0T105T.W6z5LvREJJHMXxIGOLc4w9aQUM-1751773340848-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_de6e33016103f7116fa7a7d6ecd7b78c'
  },
  request_id: 'req_de6e33016103f7116fa7a7d6ecd7b78c',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:42:21: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1774aa6e6518-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:42:21 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=tNCFMPefKoL8JmE2tDHJtQ.oP5XLbJL70FulymMQhrM-1751773341-*******-fd_G1hvBr0do9YBITZvC4hl_vHg8RrMxp8boOoR3Ti.dHVsZEJmjTM4cNWXq7BMr.7uMKhUV4ifoE8qgNmSHI0iVr_T8Ozitb.YEApJBFQw; path=/; expires=Sun, 06-Jul-25 04:12:21 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=fK1nsGBgbNZymb6IlDN5giPaRyM8VqKttedHghDx2wA-1751773341025-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_5ea57e8039eb5b579e8e284ef33c3c0f'
  },
  request_id: 'req_5ea57e8039eb5b579e8e284ef33c3c0f',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:42:23: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1780cd4a6518-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:42:23 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=Renrznj7DEztYP.JGQyb8e1AKQ9HQiRLDhX2cvdx4dE-1751773343-*******-2G7TL8u2zaBUtQOwNDa6b8dYFI9dDROPGV40SajvvecDWCH_ikWAigf5XxwJRrN9CrHGL5tWPJbwiRILb1Wmt_VQcWNgSdduUbSXkb6dw4Y; path=/; expires=Sun, 06-Jul-25 04:12:23 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=mi_GnL6eyWAq..t8F32m0afZhpo36a5.ymn1AXXfmH8-1751773343016-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_d8d0d415be7044a1871fa46aec70cf45'
  },
  request_id: 'req_d8d0d415be7044a1871fa46aec70cf45',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:42:23: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1780cd4a6518-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:42:23 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=Renrznj7DEztYP.JGQyb8e1AKQ9HQiRLDhX2cvdx4dE-1751773343-*******-2G7TL8u2zaBUtQOwNDa6b8dYFI9dDROPGV40SajvvecDWCH_ikWAigf5XxwJRrN9CrHGL5tWPJbwiRILb1Wmt_VQcWNgSdduUbSXkb6dw4Y; path=/; expires=Sun, 06-Jul-25 04:12:23 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=mi_GnL6eyWAq..t8F32m0afZhpo36a5.ymn1AXXfmH8-1751773343016-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_d8d0d415be7044a1871fa46aec70cf45'
  },
  request_id: 'req_d8d0d415be7044a1871fa46aec70cf45',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:06: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:148:26) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac18924bf4cdc1-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:06 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=mUhhgbdnmrw3jOLD0FXDfAa1TB4CWx.JQxXo8UijOT8-1751773386-*******-jSoS7EcTNI5vc93rtNDqaStCY0be0PtFAK.C7jRngiWMylwh5Pbp_ZPYHfDMSRXqo1c1njrm1b7uVQGpIN_iRPtv2RELHgiCfI2AGbGVh6A; path=/; expires=Sun, 06-Jul-25 04:13:06 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=KEKp9Ca.eMcYxd57RfjYWhBqh41fhNdHuBT_vOYE_Kc-1751773386767-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_772c14515c8d3f3b79853ffe279f051a'
  },
  request_id: 'req_772c14515c8d3f3b79853ffe279f051a',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:08: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac189c5e1c0ec9-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:08 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=nOtKJ86NVNQLoiotb2RyimAZRLG3k317qF9K2EhZK9o-**********-*******-yq7kGMXhXWk3y2n5G51ycsNLRSA80qOtGVicj0OeE6dNqJNopJIAzjZeOQxfPVMOsqIjccGIppunreaKNM1cxW1Osjerh5DBJ4dR_oHu_8s; path=/; expires=Sun, 06-Jul-25 04:13:08 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=QCX6EHMdepmfjBSjfHc12tIhlKmAChfTopi53Jqvgzo-**********324-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_be776e2a8a2d992b859d3112b1ace134'
  },
  request_id: 'req_be776e2a8a2d992b859d3112b1ace134',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:08: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac189def750ec9-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:08 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=FrvEiX6OQaH4A0tnnwhFuy9OxRg99CjZhoc90l57a3o-**********-*******-nTOal85IxFqmCSzxRwxSFWEmoscFgnCT3Tz3d.tnTnDunh3cnchYcP7vjLFwN0y8lfpMkD7qPK7rALwepYJ4hHboDMUkP988Bvzka8eHjLw; path=/; expires=Sun, 06-Jul-25 04:13:08 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=UIIsyAFW5JqitaVcOQCH1wFmNhs0lcCDqXaF.v1HjoE-**********566-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_b0a239a2f13c5a83a3b9ea88b490197a'
  },
  request_id: 'req_b0a239a2f13c5a83a3b9ea88b490197a',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:10: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac18a9f88d0ec9-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:10 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=7pp5x4zsys7YAoGfMNoRzne0D1ubRHkWevmvQpN0qd8-1751773390-*******-zDoZmjvCTrZEhD5iFqovW6usMA9NUpbkwBxkrBmVLPjavUj7cQd.i2if_lCU0MunjS5W_95qolJxEl7hmjWyietMW53xgRuYH7XbIpJP8Bw; path=/; expires=Sun, 06-Jul-25 04:13:10 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=nQgbXk1fOnciX6c4UQDy8t59ev5_0uv1UYhJDDMkDX0-1751773390617-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_381cc629715633cb66f860bd9f564822'
  },
  request_id: 'req_381cc629715633cb66f860bd9f564822',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:10: Message processing error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac18a9f88d0ec9-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:10 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=7pp5x4zsys7YAoGfMNoRzne0D1ubRHkWevmvQpN0qd8-1751773390-*******-zDoZmjvCTrZEhD5iFqovW6usMA9NUpbkwBxkrBmVLPjavUj7cQd.i2if_lCU0MunjS5W_95qolJxEl7hmjWyietMW53xgRuYH7XbIpJP8Bw; path=/; expires=Sun, 06-Jul-25 04:13:10 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=nQgbXk1fOnciX6c4UQDy8t59ev5_0uv1UYhJDDMkDX0-1751773390617-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_381cc629715633cb66f860bd9f564822'
  },
  request_id: 'req_381cc629715633cb66f860bd9f564822',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:27: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:148:26) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac19116b4c634d-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:27 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=QHmpJ3xnXc8GHiPP3E19.cmaLnguDRoWZjrw2A8Anog-1751773407-*******-fo0dRy5x3zdGg2FTztJwUu9C0YpOW76iIe0d7DeYTqhassD3mNPELTBtfAQCntc56fjIqIDstiw4trbz0djxJnn5N.rMZQEMT.hsYXBotDo; path=/; expires=Sun, 06-Jul-25 04:13:27 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=uo6Z9qjyVIsE40TtIjWW2TPeqK3D1CncBQTO2CB6XY4-1751773407126-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_bb63e1e1a98ba961fbbfb4a33d0c6979'
  },
  request_id: 'req_bb63e1e1a98ba961fbbfb4a33d0c6979',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:28: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac191ca9a303bb-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:28 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=dTuYJVthDQceNxns_du9i86SqBqWGgKKP.ps4Ek0G2s-1751773408-*******-ENfPAEk7WQxj_LG6HMWLsaVxt0pkciDI5OqVO3IoCANfrQ59pyQ6r2W1F_NjsakN37QNyCxGcIAIvOFIGa6_1YIhfuaQtX82wdmp_I3ARMw; path=/; expires=Sun, 06-Jul-25 04:13:28 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=0hWv_ITOkk1cvRB40KM6vqi8y1JpX3tR8eJmlXXB_4k-1751773408865-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_f5ac82858bb05242e447d275c73fe373'
  },
  request_id: 'req_f5ac82858bb05242e447d275c73fe373',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:28: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac191ccdf5634d-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:28 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=Bo4iypyJWgyBko77lBps2rJX7QRRcBBBbPYeqm5eUQE-1751773408-*******-h0.18wW.0eICpHJzEGH2wGXwJ.OFbNzguvIluzRWoBGX2ae9h3dsbsrY1RrUQXg2lpqrWeV.G9gTXdhwqCTn55ekWzRxeoffQJXbvqR7pjI; path=/; expires=Sun, 06-Jul-25 04:13:28 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=Ud12Ca.GH8gpNRJtsO2wHmwABS3VFl6ckluy4nQUA3U-1751773408879-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_30708102d8813f2124ee98b818339cf7'
  },
  request_id: 'req_30708102d8813f2124ee98b818339cf7',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:30: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac192868d1634d-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:30 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=3Sgptehl8f5hASmG73spGFC2QuQh4gOkIqhW8wqyPok-1751773410-*******-9LPn2P5Pm8rYy9JuY7Wxhy76UIcxhDcpk5lPCOpxZMDEFk6gYqyNDLwIl1CQBzDuqPK4.hd7abRG24FZSrOfTvrqMS82PABmdy0q936fHIA; path=/; expires=Sun, 06-Jul-25 04:13:30 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=jBnhe8lhKC_nZx4R5JxlYjNYeyoaeLHBH3z3KSOhstM-1751773410792-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_e66ff3953ca6b0ad802df1a5882a3d7b'
  },
  request_id: 'req_e66ff3953ca6b0ad802df1a5882a3d7b',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:43:30: Message processing error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac192868d1634d-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:43:30 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=3Sgptehl8f5hASmG73spGFC2QuQh4gOkIqhW8wqyPok-1751773410-*******-9LPn2P5Pm8rYy9JuY7Wxhy76UIcxhDcpk5lPCOpxZMDEFk6gYqyNDLwIl1CQBzDuqPK4.hd7abRG24FZSrOfTvrqMS82PABmdy0q936fHIA; path=/; expires=Sun, 06-Jul-25 04:13:30 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=jBnhe8lhKC_nZx4R5JxlYjNYeyoaeLHBH3z3KSOhstM-1751773410792-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_e66ff3953ca6b0ad802df1a5882a3d7b'
  },
  request_id: 'req_e66ff3953ca6b0ad802df1a5882a3d7b',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:03: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:148:26) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac19f29b4f4176-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:03 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=0Zjvb2TR5qw1B8Z6r7mqUP4CmMvUNEVXRDK0Vjwwn_k-1751773443-*******-Av1xIj_1NbpHG6h05iWXOrsPj.ytZ5oqTHSkJYD1cktOAS0gA1A2EPRqcvgT1xOG5eAiKnGbPAkNsm6XxZnimaHKiITg.nGxmyeMYG8M2lE; path=/; expires=Sun, 06-Jul-25 04:14:03 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=HQm1_6rIfmo812B0PEKnAIGgwY8e4hgklmqv.t7pQ7Q-1751773443139-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_cb47b3fc3ff363f5ad0afbc55ffa7e63'
  },
  request_id: 'req_cb47b3fc3ff363f5ad0afbc55ffa7e63',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:04: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac19fd1ef04176-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:04 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=UigpYSF2kFvM1H3dAMFhf_OwvZb3t.ZGkng1_JU8iT0-1751773444-*******-JyzlWcUjpvtACbxRRl9QGYGD5YdGLtiiI9GwjdHlZ0WiSPj1.w4exKbvSQFaCJryGOK421WbhKIhP4kA5v6YsmYC4wNFvNrig03yxE8EytE; path=/; expires=Sun, 06-Jul-25 04:14:04 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=5E_Zn0kwKM4fVzb9nwxcqW4J40GwXbvLOL0MYkXcufM-1751773444760-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_2ba92f94affa3a713f00c11a7d256b2d'
  },
  request_id: 'req_2ba92f94affa3a713f00c11a7d256b2d',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:04: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac19fe2f3b4176-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:04 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=UjG_6FilIj1JOxgQ_ujU_Xjr0F8P3MsuraU1hbzbn7I-1751773444-*******-HEWEhHfkFuejolglS_I_nzQJGwgFcslqIoUdWyRofAhTmjzsSq5V3.lRAnIlLXzQXytS0GuB9lVL7CbbUlP9GV17bQZr0fdrKD1sVpIjZVg; path=/; expires=Sun, 06-Jul-25 04:14:04 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=rrpY8Ff_M4f..dxUvjlkHPJM_XmHE05g2lQASFvGdKo-1751773444946-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_98fa4ddf0efad6fd389d23770bf8a8c0'
  },
  request_id: 'req_98fa4ddf0efad6fd389d23770bf8a8c0',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:07: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1a099acd4176-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:07 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=5YsbA3pymwsK6C5j4nP6oejHp4Ot9y9_miz_mQ14YzY-1751773447-*******-g1hyjxP7l0d1NBaSyHCOO2yKB4tLLJ8TuwI7_sd87fgRDte5RewF4.yuOZjc.mw5ctJtHw5I9rS5llF3byF42j1f7nVSmzr3QcSxbkMjDcc; path=/; expires=Sun, 06-Jul-25 04:14:07 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=zJl7bTJ1H2lXiQ0b8lm.J3HnZj.LfQx2EzfH7wwckS4-1751773447174-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_227f656adc8e7dec9a04bc551a26a5e6'
  },
  request_id: 'req_227f656adc8e7dec9a04bc551a26a5e6',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:07: Message processing error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1a099acd4176-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:07 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=5YsbA3pymwsK6C5j4nP6oejHp4Ot9y9_miz_mQ14YzY-1751773447-*******-g1hyjxP7l0d1NBaSyHCOO2yKB4tLLJ8TuwI7_sd87fgRDte5RewF4.yuOZjc.mw5ctJtHw5I9rS5llF3byF42j1f7nVSmzr3QcSxbkMjDcc; path=/; expires=Sun, 06-Jul-25 04:14:07 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=zJl7bTJ1H2lXiQ0b8lm.J3HnZj.LfQx2EzfH7wwckS4-1751773447174-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_227f656adc8e7dec9a04bc551a26a5e6'
  },
  request_id: 'req_227f656adc8e7dec9a04bc551a26a5e6',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:15: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:44:45: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1afa08ba9533-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=ubrYkmTtkmXskCkCca9nfmfCCHJ9hD9OzOCtadpdENc-1751773485-*******-I_p7wbd1wHwPj45NnqymPZGWs1H2GaUPwJ0gAIEl0NSzrLXk.AT.wcUlpyuGKZBDIFoY0Wnh0dFXz7RgStwdFJjRQN4zu0qPNj1cWjKT1f4; path=/; expires=Sun, 06-Jul-25 04:14:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=_LFPBIc2ZzfT_Q26MeZEq63Pa7861VdIjrokVwzV9hQ-1751773485286-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_558b341160621ec12e73287888c3d19e'
  },
  request_id: 'req_558b341160621ec12e73287888c3d19e',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:46: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1b04dbd89533-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:46 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=aLqXkX4L66Fn0EIi0FfoBxwZMpJ8hWfqKnyPgrmaCes-1751773486-*******-Uzyceux5YQ4oUWYscH79o8H1Bj2.vFrJck0HFdzNiGNofr33wLJrC4AHxY5l6ZAArf8hQG4FbUGHfUA56nNJTY5D2OffNaEpXUf37cDEpnI; path=/; expires=Sun, 06-Jul-25 04:14:46 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=4Nai_jjbDfxIgwqpMG9oxv.JtXDoSNSoJtEuhqbCyMQ-1751773486974-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_4483feb36bea271081ad4004ddfbcd9b'
  },
  request_id: 'req_4483feb36bea271081ad4004ddfbcd9b',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:47: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1b061c279533-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:47 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=WauoiiKMNMNxHDB1T08ys1Zf.joqVFukIrGd6Q8lFVM-1751773487-*******-MqtuvNUPeMtdn0jdaHMoBdySbFFeUQLnC5E.w76qVuGwRrjTLgBypACg0g2JIMUloNd669uh_R1.yDOxxU.K849blPopLQ1CGmJ_N7kwxro; path=/; expires=Sun, 06-Jul-25 04:14:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=uLtqeO6nmqgQm7Xr0dAhkjXxVI2F0HIaKFRrW4RakvQ-1751773487159-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_36094f1dfb1879fc10f67b9d0c1a84ef'
  },
  request_id: 'req_36094f1dfb1879fc10f67b9d0c1a84ef',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:49: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1b11afc69533-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:49 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=iPyTo_a.j1N.1U3Mmnp83vbWdkdmzBA2rIcA_MEfyWg-1751773489-*******-6LR7KCRb6NDieFvIpfDWU.9QRk94u53UzntTcMniL1lg_s0g5kl7Z7HEdMZ1qYawnfQ0XWvYnrEBxHqDH5ihAq667Uur2vN5ki3HxeRg91w; path=/; expires=Sun, 06-Jul-25 04:14:49 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=SxBAHJ8GfM3m805RIW0KDakp6bAHx3m5yp9EaNHR6_g-1751773489092-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_d1072df8a870e3cba5fb568908ca3973'
  },
  request_id: 'req_d1072df8a870e3cba5fb568908ca3973',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:44:49: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1b11afc69533-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:44:49 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=iPyTo_a.j1N.1U3Mmnp83vbWdkdmzBA2rIcA_MEfyWg-1751773489-*******-6LR7KCRb6NDieFvIpfDWU.9QRk94u53UzntTcMniL1lg_s0g5kl7Z7HEdMZ1qYawnfQ0XWvYnrEBxHqDH5ihAq667Uur2vN5ki3HxeRg91w; path=/; expires=Sun, 06-Jul-25 04:14:49 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=SxBAHJ8GfM3m805RIW0KDakp6bAHx3m5yp9EaNHR6_g-1751773489092-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_d1072df8a870e3cba5fb568908ca3973'
  },
  request_id: 'req_d1072df8a870e3cba5fb568908ca3973',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:27: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:148:26) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c016db59bc3-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:27 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=w.qO5RWCVl_1q1mlZh_Coq76nhW1_t4h2m.N2w.K9mE-1751773527-*******-trQExvCOs3VAbGhFsXI917TZRCgE16Cuk9NI89AKCAjCy3sV3b5RmyBsp_LEnbvZW2z9fd.NcKABK5CQxBVfH9gdm_FZYn0Jqv9sEv7sT_8; path=/; expires=Sun, 06-Jul-25 04:15:27 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=ymtfvLWQgLH_93qZ.SQCEvtunwVGPfS7jZOCAlpWmSY-1751773527427-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_c1ec9c905c2c9080c7cb53120620c31f'
  },
  request_id: 'req_c1ec9c905c2c9080c7cb53120620c31f',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:29: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c0c3d1f9bc3-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:29 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=mpBM6KjTZ_G_6SI2iq.cfYbFxdqql.q_qlUVhhuoE5E-1751773529-*******-HmmW5Hdxlso0uKIZYWY3m6nI9Lhq4PlcRZLd.sK6uFMlw.M0IxrKkBs_DRrvw_5Wrt5dHh1ngucz0UG4P7p6sWTj154_kX2gQfd5kmcGF.Y; path=/; expires=Sun, 06-Jul-25 04:15:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=sm1FJn9k1prsZigK9EevJusW.lAYlCU3PqT0jeEFd5U-1751773529106-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_e1f5fd5f5a44277e2f29ae75a1331ba0'
  },
  request_id: 'req_e1f5fd5f5a44277e2f29ae75a1331ba0',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:29: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:164:29) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c0caa9063ca-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:29 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=74IKG5PiENsSG7hPSSgGMp_TFHfxmwAZQvNu_UnCjrM-1751773529-*******-3rNK0uy4FsU0wHQMAxpuWcCLeRuTmPEwqT8oelDsF6DSmmI.IlviaIxcr6ahQunS7Do02MjbTuRMKPL5NKqPYkMrLO2y2gDygNsNaNRHuQ0; path=/; expires=Sun, 06-Jul-25 04:15:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=cTO8SaVP3SF3l17UXmtsptQwlfsnIuxe1TYdmLho47E-1751773529189-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_08b90f542d865d4cecdd0e09d5adad7f'
  },
  request_id: 'req_08b90f542d865d4cecdd0e09d5adad7f',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:31: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c191d7463ca-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:31 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=Oaj1u4ritCsaDxtVX44w9CPvZvqHB6kZM.80nzh70vo-1751773531-*******-Y1g_h9thiJ16_9MfD2xvOvpht4QkWSII84BDjBufJuVIoddQc8RhGkYZMk2IYlR_n70cxgT8ND1nlWgeojrpODe05xxlDEE0r4G37hxN8ME; path=/; expires=Sun, 06-Jul-25 04:15:31 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=SZGlRz9qrToOj2pX7ECnd7vprkg3PTH.P7Tmx6d.5L0-1751773531233-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_621bbf751d96abdd89dad42475b1ede6'
  },
  request_id: 'req_621bbf751d96abdd89dad42475b1ede6',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:31: Message processing error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async Socket.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:194:24) {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c191d7463ca-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:31 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=Oaj1u4ritCsaDxtVX44w9CPvZvqHB6kZM.80nzh70vo-1751773531-*******-Y1g_h9thiJ16_9MfD2xvOvpht4QkWSII84BDjBufJuVIoddQc8RhGkYZMk2IYlR_n70cxgT8ND1nlWgeojrpODe05xxlDEE0r4G37hxN8ME; path=/; expires=Sun, 06-Jul-25 04:15:31 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=SZGlRz9qrToOj2pX7ECnd7vprkg3PTH.P7Tmx6d.5L0-1751773531233-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_621bbf751d96abdd89dad42475b1ede6'
  },
  request_id: 'req_621bbf751d96abdd89dad42475b1ede6',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:45: Intent analysis error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.analyzeUserIntent (/home/<USER>/ai-product-support-agent/src/config/openai.js:139:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:176:24
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c710c339526-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:45 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=X02AwTlweM.D9XmmWtP8LUkrHsjzPXasaLVlB5u3IC8-1751773545-*******-BFuxujRa.PrJ2PNbdZ1YtiMeep0UjJ7fDBwyyryD6JigCmYuBdXk4Kd_ApvUu43PshKed.zs5QfbnDklaGq2_4Afgd03YwIy.cYn0mK2uGE; path=/; expires=Sun, 06-Jul-25 04:15:45 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=gAV9iKkTHRKzF8pqy0WL8duvO_NrXgyITiM2sZWuqhQ-1751773545308-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_db5bb2f067d32a36f795f85ef2beb43f'
  },
  request_id: 'req_db5bb2f067d32a36f795f85ef2beb43f',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:46: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchDocuments (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:94:27)
    at async KnowledgeBaseService.searchDocumentsWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:367:21)
    at async Promise.all (index 1)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c7b3f189526-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:46 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=PYmCCCQNmJskPl2hCiqpbflbffdanlwBZoJl0oojOFk-1751773546-*******-m3pBVWnTHKk8wQzRYAgrYp1ag1mD1Ob.3P.DgybqhUqil9pCjO8DRW8b4br47Q_7lisIAY09KrO2_lOibzgFqegschoT1AE5LFwTEGJiTkk; path=/; expires=Sun, 06-Jul-25 04:15:46 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=v1kipb_Z..Fva6QFscPD9k2dDTkaNhpDhNULC.xRf4A-1751773546853-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_53eb5c6e5fc8a950d0723bf792e6fa78'
  },
  request_id: 'req_53eb5c6e5fc8a950d0723bf792e6fa78',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:46: OpenAI embedding error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateEmbedding (/home/<USER>/ai-product-support-agent/src/config/openai.js:40:24)
    at async KnowledgeBaseService.searchKnowledgeBase (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:16:27)
    at async KnowledgeBaseService.searchKnowledgeBaseWithContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:346:21)
    at async Promise.all (index 0)
    at async KnowledgeBaseService.getRelevantContext (/home/<USER>/ai-product-support-agent/src/services/knowledgeBase.js:306:39)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:179:25
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c7b7bcd7a09-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:46 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=nQDq8X977DoG7ND.awPVmj_g0a8gPsluSDZ2TO58IAw-1751773546-*******-XQqstJqvIH9AAPZOvedy4wzz0EdgqFB0RYgzQc1p_AsQkxmFgMxORgi2sTkeawKMr8D2Fk2eAvEX3trX8lvLgKW3r7KtjKVQCvhFjQZWw.8; path=/; expires=Sun, 06-Jul-25 04:15:46 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=wnme1KK1QyR0MQ5roY4bLj5o8jl99d2vRFqfKf8qy1Y-1751773546916-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_2e5fd6f581994f1718aeee1cc46eb6e0'
  },
  request_id: 'req_2e5fd6f581994f1718aeee1cc46eb6e0',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:49: OpenAI API error: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c882ce07a09-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:48 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=lnyzDjS5dYNA9PrjxCovq..cjQnLLGg4ioew4aF9SeU-1751773548-*******-ov85OjC.1QILbXAwJO.OZqQJe.TuUyxgwF2.b9y096oJHaFnaep3rygg1d9DO4HgvhuOTiMHk4nKkGBQkq2R4iohQb89R1h6J4o6V4ublsw; path=/; expires=Sun, 06-Jul-25 04:15:48 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=RF9bPJLxwZZ2KN39RaQMiOHgVh3XZjO25GuU1rhq_Tg-1751773548994-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_70b6b3e0ccb296c14d2b0351a130fcaa'
  },
  request_id: 'req_70b6b3e0ccb296c14d2b0351a130fcaa',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:45:49: Error processing message: RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
    at APIError.generate (/home/<USER>/ai-product-support-agent/node_modules/openai/error.js:63:20)
    at OpenAI.makeStatusError (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:302:33)
    at OpenAI.makeRequest (/home/<USER>/ai-product-support-agent/node_modules/openai/core.js:346:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async OpenAIService.generateResponse (/home/<USER>/ai-product-support-agent/src/config/openai.js:21:24)
    at async /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:219:26
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21 {
  status: 429,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '95ac1c882ce07a09-LHR',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Sun, 06 Jul 2025 03:45:48 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=lnyzDjS5dYNA9PrjxCovq..cjQnLLGg4ioew4aF9SeU-1751773548-*******-ov85OjC.1QILbXAwJO.OZqQJe.TuUyxgwF2.b9y096oJHaFnaep3rygg1d9DO4HgvhuOTiMHk4nKkGBQkq2R4iohQb89R1h6J4o6V4ublsw; path=/; expires=Sun, 06-Jul-25 04:15:48 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=RF9bPJLxwZZ2KN39RaQMiOHgVh3XZjO25GuU1rhq_Tg-1751773548994-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_70b6b3e0ccb296c14d2b0351a130fcaa'
  },
  request_id: 'req_70b6b3e0ccb296c14d2b0351a130fcaa',
  error: {
    message: 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota'
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota'
}
2025-07-06T04:46:13: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:47:30: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:48:43: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:50:32: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:52:35: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T04:54:33: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T04:55:40: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:410:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:268:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:283:13)
    at process.emit (node:events:518:28)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:04:44: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:04:44: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:04:44: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:04:44: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:04:44: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:04:44: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:04:44: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:05:30: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/getMe
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T05:05:51: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:410:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:268:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:283:13)
    at process.emit (node:events:518:28)
2025-07-06T05:15:40: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:24:00: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T05:31:38: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:33:01: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T05:41:21: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T06:00:54: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T06:09:14: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T14:55:08: Knowledge base search error: {
  code: 'PGRST100',
  details: `unexpected "'" expecting letter, digit, "-", "->>", "->" or delimiter (.)`,
  hint: null,
  message: `"failed to parse logic tree ((title.ilike.%I''m working with the wa65f but the touch seems to be locked, I can see a unusual icon on the screen but any attempts to touch just don''t do anything%, content.ilike.%I''m working with the wa65f but the touch seems to be locked, I can see a unusual icon on the screen but any attempts to touch just don''t do anything%))" (line 1, column 152)`
}
2025-07-06T14:55:08: Document search error: {
  code: 'PGRST100',
  details: `unexpected "'" expecting letter, digit, "-", "->>", "->" or delimiter (.)`,
  hint: null,
  message: `"failed to parse logic tree ((original_name.ilike.%I''m working with the wa65f but the touch seems to be locked, I can see a unusual icon on the screen but any attempts to touch just don''t do anything%, content.ilike.%I''m working with the wa65f but the touch seems to be locked, I can see a unusual icon on the screen but any attempts to touch just don''t do anything%))" (line 1, column 160)`
}
2025-07-06T14:57:48: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T14:59:17: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T15:07:37: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T16:45:18: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T16:53:04: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:410:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:268:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:283:13)
    at process.emit (node:events:518:28)
2025-07-06T16:58:12: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:410:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:270:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:285:13)
    at process.emit (node:events:518:28)
2025-07-06T17:06:23: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T17:08:41: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T17:11:08: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T17:14:43: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T17:17:01: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T17:19:28: Unhandled Rejection at: Promise {
  <rejected> FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
      at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
      at listOnTimeout (node:internal/timers:588:17)
      at process.processTimers (node:internal/timers:523:7) {
    type: 'request-timeout'
  }
} reason: FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7) {
  type: 'request-timeout'
}
2025-07-06T17:54:33: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:459:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:270:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:285:13)
    at process.emit (node:events:518:28)
2025-07-06T18:00:03: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:360:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:00:03: Bot error: ReferenceError: userMessage is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:379:64
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:01:49: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:03:48: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:05:05: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:08:40: Failed to send acknowledgment: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
2025-07-06T18:08:43: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:360:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:10:39: Failed to send acknowledgment: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
2025-07-06T18:10:41: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:10:43: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:360:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:11:53: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:360:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:11:53: Bot error: ReferenceError: userMessage is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:379:64
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:11:56: Failed to send acknowledgment: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
2025-07-06T18:11:58: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:360:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:17:32: Failed to send acknowledgment: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
2025-07-06T18:17:34: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:360:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:19:35: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:21:23: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:528:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:270:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:291:13)
    at process.emit (node:events:518:28)
2025-07-06T18:30:11: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:498:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:30:11: Bot error: ReferenceError: userMessage is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:517:64
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:32:03: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:32:50: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:33:58: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:34:40: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:498:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:34:40: Bot error: ReferenceError: userMessage is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:517:64
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:38:54: Failed to send acknowledgment: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
2025-07-06T18:38:58: Error processing message: ReferenceError: searchResults is not defined
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:498:26
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
2025-07-06T18:39:37: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:666:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:270:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:291:13)
    at process.emit (node:events:518:28)
2025-07-06T18:40:14: Error starting bot in polling mode: Error: Bot launch timeout
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:774:35)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T18:42:09: Bot error: TypeError: Cannot read properties of undefined (reading 'id')
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:50:31
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:159:31
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:165:111
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: Cannot determine `updateType` of {"test":"webhook"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:586:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T18:42:09: Unhandled Rejection at: Promise {
  <rejected> Error: Cannot determine `updateType` of {"test":"webhook"}
      at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
      at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
      at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
      at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
      at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:586:18)
      at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
} reason: Error: Cannot determine `updateType` of {"test":"webhook"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:586:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T18:44:05: Failed to send acknowledgment: 400: Bad Request: chat not found
2025-07-06T18:44:05: Failed to send typing indicator: 400: Bad Request: chat not found
2025-07-06T18:44:07: Failed to send chunked response: 400: Bad Request: chat not found
2025-07-06T18:44:07: Error in async processing: TelegramError: 400: Bad Request: chat not found
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramUX.sendChunkedResponse (/home/<USER>/ai-product-support-agent/src/utils/telegramUX.js:186:7)
    at async TelegramBotService.processMessageAsync (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:709:14) {
  response: {
    ok: false,
    error_code: 400,
    description: 'Bad Request: chat not found'
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 987654321,
      message_thread_id: undefined,
      text: "It looks like you're testing the message functionality. If you have any questions or need assistance regarding Samsung interactive displays, feel free to ask!"
    }
  }
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: 400: Bad Request: chat not found
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-07-06T18:44:07: Unhandled Rejection at: Promise {
  <rejected> TelegramError: 400: Bad Request: chat not found
      at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    response: {
      ok: false,
      error_code: 400,
      description: 'Bad Request: chat not found'
    },
    on: { method: 'sendMessage', payload: [Object] }
  }
} reason: TelegramError: 400: Bad Request: chat not found
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  response: {
    ok: false,
    error_code: 400,
    description: 'Bad Request: chat not found'
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 987654321,
      message_thread_id: undefined,
      text: "🤖 I'm experiencing some technical difficulties right now. Please try:\n" +
        '\n' +
        '• Using specific commands like /help or /categories\n' +
        '• Asking simpler, more direct questions\n' +
        '• Trying again in a moment'
    }
  }
}
2025-07-06T18:46:50: Failed to send acknowledgment: 400: Bad Request: chat not found
2025-07-06T18:46:51: Failed to send typing indicator: 400: Bad Request: chat not found
2025-07-06T18:46:53: Failed to send chunked response: 400: Bad Request: chat not found
2025-07-06T18:46:53: Error in async processing: TelegramError: 400: Bad Request: chat not found
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramUX.sendChunkedResponse (/home/<USER>/ai-product-support-agent/src/utils/telegramUX.js:186:7)
    at async TelegramBotService.processMessageAsync (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:709:14) {
  response: {
    ok: false,
    error_code: 400,
    description: 'Bad Request: chat not found'
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 987654321,
      message_thread_id: undefined,
      text: '❌ \n' +
        '*Issue:*\n' +
        ' Screen not working\n' +
        '\n' +
        '🛠️ \n' +
        '*Troubleshooting Steps:*\n' +
        '\n' +
        '1️⃣ \n' +
        '*🔍 Check Power 🔌 Connection:*\n' +
        '\n' +
        '   - 🔍 Ensure that the power 🔌 cable is securely 🔌 connected to both the display and the power outlet.\n' +
        '\n' +
        '2️⃣ \n' +
        '*Test Power Outlet:*\n' +
        '\n' +
        "   - 🔌 Plug something else into the same outlet to confirm it's working.\n" +
        '\n' +
        '3️⃣ \n' +
        '*Power Button:*\n' +
        '\n' +
        '   - 🔍 Make sure the power button on the display is turned on.\n' +
        '\n' +
        '4️⃣ \n' +
        '*Remote Control:*\n' +
        '\n' +
        '   - If you are using a remote, 🔍 check if the batteries are functional.\n' +
        '\n' +
        'Please follow these steps and let me know if the screen turns on or if you need further assistance!'
    }
  }
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: 400: Bad Request: chat not found
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-07-06T18:46:53: Unhandled Rejection at: Promise {
  <rejected> TelegramError: 400: Bad Request: chat not found
      at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    response: {
      ok: false,
      error_code: 400,
      description: 'Bad Request: chat not found'
    },
    on: { method: 'sendMessage', payload: [Object] }
  }
} reason: TelegramError: 400: Bad Request: chat not found
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  response: {
    ok: false,
    error_code: 400,
    description: 'Bad Request: chat not found'
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 987654321,
      message_thread_id: undefined,
      text: "🔧 I'm having trouble accessing the knowledge base right now. For immediate help:\n" +
        '\n' +
        '• Try restarting your device\n' +
        '• Check all cable connections\n' +
        '• Use /help for available commands\n' +
        '• Contact <NAME_EMAIL>'
    }
  }
}
2025-07-06T19:03:14: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T19:05:20: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:795:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T19:42:42: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:842:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T19:46:53: Bot error: TypeError: Cannot read properties of undefined (reading 'id')
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:97:31
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:159:31
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:165:111
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: Cannot determine `updateType` of {"test":"data"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T19:46:53: Unhandled Rejection at: Promise {
  <rejected> Error: Cannot determine `updateType` of {"test":"data"}
      at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
      at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
      at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
      at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
      at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
      at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
} reason: Error: Cannot determine `updateType` of {"test":"data"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T19:47:24: Bot error: TypeError: Cannot read properties of undefined (reading 'id')
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:97:31
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:159:31
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:165:111
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: Cannot determine `updateType` of {"test":"data"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T19:47:24: Unhandled Rejection at: Promise {
  <rejected> Error: Cannot determine `updateType` of {"test":"data"}
      at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
      at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
      at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
      at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
      at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
      at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
} reason: Error: Cannot determine `updateType` of {"test":"data"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T19:48:05: Bot error: TypeError: Cannot read properties of undefined (reading 'id')
    at /home/<USER>/ai-product-support-agent/src/services/telegramBot.js:97:31
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:23)
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:27
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:159:31
    at /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:165:111
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: Cannot determine `updateType` of {"test":"data"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T19:48:05: Unhandled Rejection at: Promise {
  <rejected> Error: Cannot determine `updateType` of {"test":"data"}
      at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
      at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
      at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
      at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
      at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
      at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
} reason: Error: Cannot determine `updateType` of {"test":"data"}
    at get updateType (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:23:15)
    at Context.assert (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:156:84)
    at Context.sendMessage (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:281:14)
    at Context.reply (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/context.js:291:21)
    at Telegraf.handleError (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:633:18)
    at Telegraf.handleUpdate (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:236:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/webhook.js:51:16
2025-07-06T19:53:40: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:842:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T20:07:56: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:842:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T20:12:18: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:842:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T20:14:38: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:842:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T20:20:06: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:842:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T20:21:42: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:842:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T20:25:29: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:900:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T20:30:42: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:974:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T21:14:26: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:864:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T21:21:02: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:864:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T21:38:30: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:864:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T21:51:44: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:864:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T21:56:59: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1317:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T22:04:27: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1324:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T22:07:28: Error sending document downloads: TelegramError: 400: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.processMessageAsync (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:835:11) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166-686e-42fb-acef-f0de9398f9f9-Samsung_WA65F_User_Manual.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:08:49: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1598:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T22:09:40: Error sending document downloads: TelegramError: 400: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.processMessageAsync (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:835:11) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166-686e-42fb-acef-f0de9398f9f9-Samsung_WA65F_User_Manual.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:10:38: Error handling manual request: TelegramError: 400: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.handleManualRequest (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1103:9)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166-686e-42fb-acef-f0de9398f9f9-Samsung_WA65F_User_Manual.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:10:56: Error handling manual request: TelegramError: 400: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.handleManualRequest (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1103:9)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166-686e-42fb-acef-f0de9398f9f9-Samsung_WA65F_User_Manual.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:13:26: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1621:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T22:14:21: Error handling manual request: TelegramError: 400: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.handleManualRequest (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1103:9)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166-686e-42fb-acef-f0de9398f9f9-Samsung_WA65F_User_Manual.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:15:24: Error handling manual request: TelegramError: 400: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.handleManualRequest (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1103:9)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166-686e-42fb-acef-f0de9398f9f9-Samsung_WA65F_User_Manual.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:16:17: Error handling manual request: TelegramError: 400: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.handleManualRequest (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1103:9)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 100"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166-686e-42fb-acef-f0de9398f9f9-Samsung_WA65F_User_Manual.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:19:02: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1661:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T22:19:17: Error handling manual request: TelegramError: 400: Bad Request: inline keyboard button URL 'http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4' is invalid: Wrong HTTP URL
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.handleManualRequest (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1103:9)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: inline keyboard button URL 'http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4' is invalid: Wrong HTTP URL"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166\\-686e\\-42fb\\-acef\\-f0de9398f9f9\\-Samsung\\_WA65F\\_User\\_Manual\\.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:19:40: Error handling manual request: TelegramError: 400: Bad Request: inline keyboard button URL 'http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4' is invalid: Wrong HTTP URL
    at Telegram.callApi (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/core/network/client.js:315:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async TelegramBotService.handleManualRequest (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1103:9)
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17)
    at async /home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:519:21
    at async execute (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/composer.js:518:17) {
  response: {
    ok: false,
    error_code: 400,
    description: "Bad Request: inline keyboard button URL 'http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4' is invalid: Wrong HTTP URL"
  },
  on: {
    method: 'sendMessage',
    payload: {
      chat_id: 5560551341,
      message_thread_id: undefined,
      parse_mode: 'Markdown',
      reply_markup: [Object],
      disable_web_page_preview: true,
      text: '📚 **Related Documents & Manuals**\n' +
        '\n' +
        '📖 **282fc166\\-686e\\-42fb\\-acef\\-f0de9398f9f9\\-Samsung\\_WA65F\\_User\\_Manual\\.txt**\n' +
        '📥 [Download](http://localhost:3000/files/3f573e20-4b5d-429d-83d0-91a6f9dedeb4)\n' +
        '💡 Samsung WA65F Interactive Display User Manual\n' +
        '\n' +
        'GETTING STARTED\n' +
        '\n' +
        'Thank you for purchasing the Samsung...\n' +
        '\n' +
        '💡 **Tip**: Click the download links above to get the full documents!'
    }
  }
}
2025-07-06T22:20:28: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1672:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T22:23:00: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1684:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
2025-07-06T22:23:16: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:16: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:16: File serve error: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:25: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:25: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:25: File serve error: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:37: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:37: Error fetching document: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:23:37: File serve error: {
  code: 'PGRST116',
  details: 'The result contains 0 rows',
  hint: null,
  message: 'JSON object requested, multiple (or no) rows returned'
}
2025-07-06T22:24:34: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:1684:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:274:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:289:13)
    at process.emit (node:events:518:28)
