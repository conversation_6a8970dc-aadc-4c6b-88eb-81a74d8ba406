2025-07-06T02:10:29: Bot error: TimeoutError: Promise timed out after 90000 milliseconds
    at Timeout._onTimeout (/home/<USER>/ai-product-support-agent/node_modules/p-timeout/index.js:39:64)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
FetchError: network timeout at: https://api.telegram.org/bot7586114824:[REDACTED]/sendMessage
    at Timeout.<anonymous> (/home/<USER>/ai-product-support-agent/node_modules/node-fetch/lib/index.js:1494:13)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
2025-07-06T02:22:03: Error stopping bot: Error: Bot is not running!
    at Telegraf.stop (/home/<USER>/ai-product-support-agent/node_modules/telegraf/lib/telegraf.js:218:19)
    at TelegramBotService.stop (/home/<USER>/ai-product-support-agent/src/services/telegramBot.js:242:16)
    at AIProductSupportAgent.stop (/home/<USER>/ai-product-support-agent/src/index.js:259:25)
    at process.<anonymous> (/home/<USER>/ai-product-support-agent/src/index.js:274:13)
    at process.emit (node:events:518:28)
