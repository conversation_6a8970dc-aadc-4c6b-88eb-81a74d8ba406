2025-07-06T02:22:33: 🚀 AI Product Support Agent running on port 3000
2025-07-06T02:22:33: 🌐 Web interface: http://localhost:3000
2025-07-06T02:22:33: 🤖 Telegram bot: Active
2025-07-06T02:22:33: 📊 Database: Connected
2025-07-06T02:22:33: 🧠 OpenAI: Connected
2025-07-06T02:22:33: Database connection successful
2025-07-06T02:22:33: Webhook set successfully
2025-07-06T02:22:33: Telegram bot webhook set up
2025-07-06T02:22:33: All services initialized successfully
2025-07-06T02:22:36: 
Shutting down gracefully...
2025-07-06T02:23:10: 🚀 AI Product Support Agent running on port 3000
2025-07-06T02:23:10: 🌐 Web interface: http://localhost:3000
2025-07-06T02:23:10: 🤖 Telegram bot: Active
2025-07-06T02:23:10: 📊 Database: Connected
2025-07-06T02:23:10: 🧠 OpenAI: Connected
2025-07-06T02:23:10: Database connection successful
2025-07-06T02:23:10: Webhook set successfully
2025-07-06T02:23:10: Telegram bot webhook set up
2025-07-06T02:23:10: All services initialized successfully
2025-07-06T02:23:11: User connected: KYJXz7jBM0FYTS_GAAAB
2025-07-06T02:23:12: User connected: JRF6AlcaoO6hXN5-AAAD
2025-07-06T02:23:12: User connected: Eg3gJR6KTYmIb8_VAAAF
2025-07-06T02:23:13: User connected: KopCg730QFvsTGyrAAAH
2025-07-06T02:23:14: User connected: EkU7lmRCHasZIXwmAAAJ
2025-07-06T02:23:18: User disconnected: KYJXz7jBM0FYTS_GAAAB
2025-07-06T02:25:31: User connected: o-_IA_knLNarqco-AAAO
2025-07-06T02:25:34: User disconnected: o-_IA_knLNarqco-AAAO
2025-07-06T02:26:44: User connected: i3EPZix0d6sk9EkiAAAS
2025-07-06T02:26:46: User disconnected: i3EPZix0d6sk9EkiAAAS
2025-07-06T02:26:46: User connected: 5N5_YWIfWsTQj0EUAAAU
2025-07-06T02:26:47: User disconnected: 5N5_YWIfWsTQj0EUAAAU
2025-07-06T02:26:47: User connected: ZeCJ39_jR8XLs3rpAAAW
2025-07-06T02:27:28: User disconnected: ZeCJ39_jR8XLs3rpAAAW
2025-07-06T02:30:31: User disconnected: JRF6AlcaoO6hXN5-AAAD
2025-07-06T02:32:14: 
Shutting down gracefully...
2025-07-06T02:32:22: 🚀 AI Product Support Agent running on port 3000
2025-07-06T02:32:22: 🌐 Web interface: http://localhost:3000
2025-07-06T02:32:22: 🤖 Telegram bot: Active
2025-07-06T02:32:22: 📊 Database: Connected
2025-07-06T02:32:22: 🧠 OpenAI: Connected
2025-07-06T02:32:22: Database connection successful
2025-07-06T02:32:22: Webhook set successfully
2025-07-06T02:32:22: Telegram bot webhook set up
2025-07-06T02:32:22: All services initialized successfully
2025-07-06T02:32:23: User connected: NmrfiHgcLjq3ojP9AAAB
2025-07-06T02:32:24: User connected: CbMv4iANjDQMeFl0AAAD
2025-07-06T02:32:25: User connected: -OtGsOEcBcKXZIFqAAAF
2025-07-06T02:33:14: User disconnected: -OtGsOEcBcKXZIFqAAAF
2025-07-06T02:33:15: User connected: w26lmLBzvOlVROYhAAAH
2025-07-06T02:38:25: 
Shutting down gracefully...
2025-07-06T02:38:32: 🚀 AI Product Support Agent running on port 3000
2025-07-06T02:38:32: 🌐 Web interface: http://localhost:3000
2025-07-06T02:38:32: 🤖 Telegram bot: Active
2025-07-06T02:38:32: 📊 Database: Connected
2025-07-06T02:38:32: 🧠 OpenAI: Connected
2025-07-06T02:38:33: Database connection successful
2025-07-06T02:38:33: Webhook set successfully
2025-07-06T02:38:33: Telegram bot webhook set up
2025-07-06T02:38:33: All services initialized successfully
2025-07-06T02:38:34: User connected: JdFz2g-sOqfbWKu5AAAB
2025-07-06T02:38:36: User connected: padxxnKo-Y02N5VmAAAD
2025-07-06T02:38:37: User connected: oEx0lWArv-cguZHzAAAF
2025-07-06T02:40:20: User connected: uWLd-BLZ3Q0LORhmAAAH
2025-07-06T02:40:26: User disconnected: uWLd-BLZ3Q0LORhmAAAH
2025-07-06T02:43:49: User disconnected: oEx0lWArv-cguZHzAAAF
2025-07-06T02:44:04: User disconnected: JdFz2g-sOqfbWKu5AAAB
2025-07-06T02:44:04: User connected: 8bwlO5FZYULS8WBnAAAJ
2025-07-06T02:47:43: 
Shutting down gracefully...
2025-07-06T02:47:50: 🚀 AI Product Support Agent running on port 3000
2025-07-06T02:47:50: 🌐 Web interface: http://localhost:3000
2025-07-06T02:47:50: 🤖 Telegram bot: Active
2025-07-06T02:47:50: 📊 Database: Connected
2025-07-06T02:47:50: 🧠 OpenAI: Connected
2025-07-06T02:47:51: Database connection successful
2025-07-06T02:47:51: Webhook set successfully
2025-07-06T02:47:51: Telegram bot webhook set up
2025-07-06T02:47:51: All services initialized successfully
2025-07-06T02:47:53: User connected: keUHc7YsM1gIKI5TAAAB
2025-07-06T02:47:55: User connected: e9CA_a_j9PVIFf5TAAAD
2025-07-06T02:48:30: User connected: FedpEGc5iKYqQ51HAAAF
2025-07-06T02:48:38: User disconnected: FedpEGc5iKYqQ51HAAAF
2025-07-06T02:50:35: User connected: OpIdv1oZzrFDbJFwAAAK
2025-07-06T02:51:21: User disconnected: OpIdv1oZzrFDbJFwAAAK
2025-07-06T02:52:36: User disconnected: e9CA_a_j9PVIFf5TAAAD
2025-07-06T02:53:07: User disconnected: keUHc7YsM1gIKI5TAAAB
2025-07-06T02:53:31: User connected: PUtvYx02rdkKmdhvAAAO
2025-07-06T02:53:32: User connected: NO1i7papN_EWEynjAAAQ
2025-07-06T02:53:35: User disconnected: PUtvYx02rdkKmdhvAAAO
2025-07-06T02:53:35: User connected: zg18IR3HTqh_c302AAAS
2025-07-06T02:54:11: User disconnected: zg18IR3HTqh_c302AAAS
2025-07-06T02:54:11: User connected: nm7xzFtZ6d0WWpOwAAAU
2025-07-06T02:54:17: User disconnected: nm7xzFtZ6d0WWpOwAAAU
2025-07-06T02:54:17: User connected: YAnhjWBHCS2yxjnxAAAW
2025-07-06T02:56:35: User disconnected: NO1i7papN_EWEynjAAAQ
2025-07-06T02:56:36: User disconnected: YAnhjWBHCS2yxjnxAAAW
2025-07-06T02:56:42: 
Shutting down gracefully...
2025-07-06T02:57:14: 🚀 AI Product Support Agent running on port 3000
2025-07-06T02:57:14: 🌐 Web interface: http://localhost:3000
2025-07-06T02:57:14: 🤖 Telegram bot: Active
2025-07-06T02:57:14: 📊 Database: Connected
2025-07-06T02:57:14: 🧠 OpenAI: Connected
2025-07-06T02:57:14: Database connection test failed: TypeError: fetch failed
2025-07-06T02:57:14: Tables may not exist yet - this is normal on first run
2025-07-06T02:57:15: Webhook set successfully
2025-07-06T02:57:15: Telegram bot webhook set up
2025-07-06T02:57:15: All services initialized successfully
2025-07-06T02:57:57: User connected: 5Gct4B0l_7JMxexKAAAB
2025-07-06T02:58:05: User disconnected: 5Gct4B0l_7JMxexKAAAB
2025-07-06T02:58:06: User connected: g8IbTgoJ55sYlUBcAAAD
2025-07-06T02:58:44: User connected: SeJDDeQ1H-CAxgFiAAAF
2025-07-06T03:10:08: User disconnected: g8IbTgoJ55sYlUBcAAAD
2025-07-06T03:10:09: User connected: NscE705vBdiD2OcZAAAH
2025-07-06T03:11:50: 
Shutting down gracefully...
2025-07-06T03:11:59: 🚀 AI Product Support Agent running on port 3000
2025-07-06T03:11:59: 🌐 Web interface: http://localhost:3000
2025-07-06T03:11:59: 🤖 Telegram bot: Active
2025-07-06T03:11:59: 📊 Database: Connected
2025-07-06T03:11:59: 🧠 OpenAI: Connected
2025-07-06T03:11:59: Database connection successful
2025-07-06T03:11:59: User connected: M_t6ga9jioI0-hmsAAAB
2025-07-06T03:12:00: Webhook set successfully
2025-07-06T03:12:00: Telegram bot webhook set up
2025-07-06T03:12:00: All services initialized successfully
2025-07-06T03:12:01: User connected: U3SArQdosbvSli20AAAD
2025-07-06T03:12:04: User disconnected: U3SArQdosbvSli20AAAD
2025-07-06T03:12:04: User connected: DVOA26ctLaG5S6N6AAAF
2025-07-06T03:31:15: 
Shutting down gracefully...
2025-07-06T03:31:22: 🚀 AI Product Support Agent running on port 3000
2025-07-06T03:31:22: 🌐 Web interface: http://localhost:3000
2025-07-06T03:31:22: 🤖 Telegram bot: Active
2025-07-06T03:31:22: 📊 Database: Connected
2025-07-06T03:31:22: 🧠 OpenAI: Connected
2025-07-06T03:31:22: Database connection successful
2025-07-06T03:31:23: Webhook set successfully
2025-07-06T03:31:23: Telegram bot webhook set up
2025-07-06T03:31:23: All services initialized successfully
2025-07-06T03:31:25: User connected: h34z0yeQjhNtNsP0AAAB
2025-07-06T03:31:25: User connected: NbZD3N3aCcmvL1mHAAAD
2025-07-06T03:33:18: User disconnected: h34z0yeQjhNtNsP0AAAB
2025-07-06T03:33:18: User connected: Su_rItkMTmWsNwNxAAAF
2025-07-06T03:39:57: User disconnected: Su_rItkMTmWsNwNxAAAF
2025-07-06T03:40:02: User connected: jtIReseTEyA7nJCDAAAH
2025-07-06T03:56:17: 
Shutting down gracefully...
2025-07-06T03:56:24: 🚀 AI Product Support Agent running on port 3000
2025-07-06T03:56:24: 🌐 Web interface: http://localhost:3000
2025-07-06T03:56:24: 🤖 Telegram bot: Active
2025-07-06T03:56:24: 📊 Database: Connected
2025-07-06T03:56:24: 🧠 OpenAI: Connected
2025-07-06T03:56:25: Database connection successful
2025-07-06T03:56:25: Webhook set successfully
2025-07-06T03:56:25: Telegram bot webhook set up
2025-07-06T03:56:25: All services initialized successfully
2025-07-06T03:56:26: User connected: m375eIQSS3bGIPeCAAAB
2025-07-06T03:56:26: User connected: wrNEQsdmxRY0ejwuAAAD
2025-07-06T03:56:32: 🎯 Knowledge base search for "What is the difference between wa65f and wa86c": 10 results scored
2025-07-06T03:56:32:    Top result: "What is afterimage burn-in? (from English_WEB_WAC_1030.pdf)" (score: 0.575)
2025-07-06T03:56:32: 📄 Document search for "What is the difference between wa65f and wa86c": 5 results scored
2025-07-06T03:56:32:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T03:56:37: User disconnected: m375eIQSS3bGIPeCAAAB
2025-07-06T03:56:38: User connected: EotL4IAbcwg1mPT-AAAF
2025-07-06T03:57:35: 🎯 Knowledge base search for "What are the main differences between models?": 10 results scored
2025-07-06T03:57:35:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.575)
2025-07-06T03:57:35: 📄 Document search for "What are the main differences between models?": 5 results scored
2025-07-06T03:57:35:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T03:58:52: 🎯 Knowledge base search for "What is the difference between wa86c and wm55b": 10 results scored
2025-07-06T03:58:52:    Top result: "What is afterimage burn-in? (from English_WEB_WAC_1030.pdf)" (score: 0.575)
2025-07-06T03:58:52: 📄 Document search for "What is the difference between wa86c and wm55b": 4 results scored
2025-07-06T03:58:52:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.663)
2025-07-06T04:03:33: 
Shutting down gracefully...
2025-07-06T04:03:41: 🚀 AI Product Support Agent running on port 3000
2025-07-06T04:03:41: 🌐 Web interface: http://localhost:3000
2025-07-06T04:03:41: 🤖 Telegram bot: Active
2025-07-06T04:03:41: 📊 Database: Connected
2025-07-06T04:03:41: 🧠 OpenAI: Connected
2025-07-06T04:03:41: Database connection successful
2025-07-06T04:03:41: Webhook set successfully
2025-07-06T04:03:41: Telegram bot webhook set up
2025-07-06T04:03:41: All services initialized successfully
2025-07-06T04:03:43: User connected: C3s1FZVY2MZTkIakAAAB
2025-07-06T04:03:44: User connected: AiFZL8S0fgGx3K_sAAAD
2025-07-06T04:03:47: User disconnected: C3s1FZVY2MZTkIakAAAB
2025-07-06T04:03:47: User connected: U1o_6kCRJBPx2meDAAAF
2025-07-06T04:04:03: 🎯 Knowledge base search for "hey": 1 results scored
2025-07-06T04:04:03:    Top result: "Touching does not work when HDMI connection is (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.675)
2025-07-06T04:04:03: 📄 Document search for "hey": 1 results scored
2025-07-06T04:04:03:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.675)
2025-07-06T04:04:31: 🎯 Knowledge base search for "what are the main differences between wa65c and wm75b?": 10 results scored
2025-07-06T04:04:31:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.495)
2025-07-06T04:04:31: 📄 Document search for "what are the main differences between wa65c and wm75b?": 5 results scored
2025-07-06T04:04:31:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.670)
2025-07-06T04:05:27: 🎯 Knowledge base search for "What are the main differences between wa75d and wm85b?": 10 results scored
2025-07-06T04:05:27:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.495)
2025-07-06T04:05:27: 📄 Document search for "What are the main differences between wa75d and wm85b?": 5 results scored
2025-07-06T04:05:27:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.670)
2025-07-06T04:08:15: 📄 Document search for "What are the main differences between wa65c and wm75b?": 5 results scored
2025-07-06T04:08:15:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.715)
2025-07-06T04:08:15:    Applied modifiers: device_match
2025-07-06T04:08:15: 🎯 Knowledge base search for "What are the main differences between wa65c and wm75b?": 10 results scored
2025-07-06T04:08:15:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.495)
2025-07-06T04:08:52: 🎯 Knowledge base search for "What are the main differences between wa75d and wm85b?": 10 results scored
2025-07-06T04:08:52:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.495)
2025-07-06T04:08:52: 📄 Document search for "What are the main differences between wa75d and wm85b?": 5 results scored
2025-07-06T04:08:52:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.670)
2025-07-06T04:10:04: 📄 Document search for "what are the main differences between wa75d and wm85b?": 5 results scored
2025-07-06T04:10:04:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.670)
2025-07-06T04:10:04: 🎯 Knowledge base search for "what are the main differences between wa75d and wm85b?": 10 results scored
2025-07-06T04:10:04:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.495)
2025-07-06T04:10:33: 📄 Document search for "What are the main differences between wa75d and wm85b?": 5 results scored
2025-07-06T04:10:33:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.670)
2025-07-06T04:10:33: 🎯 Knowledge base search for "What are the main differences between wa75d and wm85b?": 10 results scored
2025-07-06T04:10:33:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.495)
2025-07-06T04:11:21: 🎯 Knowledge base search for "Yes provide some general differences and usages": 10 results scored
2025-07-06T04:11:21:    Top result: "Low Blue Light (from English_WEB_WAC_1030.pdf)" (score: 0.553)
2025-07-06T04:11:21: 📄 Document search for "Yes provide some general differences and usages": 4 results scored
2025-07-06T04:11:21:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.740)
2025-07-06T04:11:21:    Applied modifiers: device_match
2025-07-06T04:13:17: 
Shutting down gracefully...
2025-07-06T04:13:24: 🚀 AI Product Support Agent running on port 3000
2025-07-06T04:13:24: 🌐 Web interface: http://localhost:3000
2025-07-06T04:13:24: 🤖 Telegram bot: Active
2025-07-06T04:13:24: 📊 Database: Connected
2025-07-06T04:13:24: 🧠 OpenAI: Connected
2025-07-06T04:13:24: Database connection successful
2025-07-06T04:13:24: Webhook set successfully
2025-07-06T04:13:24: Telegram bot webhook set up
2025-07-06T04:13:24: All services initialized successfully
2025-07-06T04:13:26: User connected: _a66dnwKZg3f3oLvAAAB
2025-07-06T04:13:27: User connected: oRj76UzHX7E2ENCTAAAD
2025-07-06T04:13:55: User disconnected: _a66dnwKZg3f3oLvAAAB
2025-07-06T04:13:55: User connected: y6BWkAUHrrrRj7L8AAAF
2025-07-06T04:14:14: 🎯 Knowledge base search for "what are the main differences between the wa75c and wm85b": 10 results scored
2025-07-06T04:14:14:    Top result: "Samsung Interactive Displays - Google EDLA and Samsung Flip UI Explained" (score: 0.495)
2025-07-06T04:14:14: 📄 Document search for "what are the main differences between the wa75c and wm85b": 5 results scored
2025-07-06T04:14:14:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.670)
2025-07-06T04:14:55: 🎯 Knowledge base search for "No I'm working with a wa65c": 10 results scored
2025-07-06T04:14:55:    Top result: "Samsung Interactive Display - Common Troubleshooting Issues" (score: 0.550)
2025-07-06T04:14:55: 📄 Document search for "No I'm working with a wa65c": 2 results scored
2025-07-06T04:14:55:    Top result: "Samsung_WA65F_User_Manual.txt" (score: 0.603)
2025-07-06T04:16:20: 🎯 Knowledge base search for "Can you provide a model comparison table?": 10 results scored
2025-07-06T04:16:20:    Top result: "Warning (from English_WEB_WAC_1030.pdf)" (score: 0.570)
2025-07-06T04:16:20: 📄 Document search for "Can you provide a model comparison table?": 5 results scored
2025-07-06T04:16:20:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.740)
2025-07-06T04:16:20:    Applied modifiers: device_match
2025-07-06T04:17:05: 🎯 Knowledge base search for "Which user manual do you have?": 10 results scored
2025-07-06T04:17:05:    Top result: "User Manual (from English_WEB_WAC_1030.pdf)" (score: 0.617)
2025-07-06T04:17:05: 📄 Document search for "Which user manual do you have?": 5 results scored
2025-07-06T04:17:05:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:17:05:    Applied modifiers: device_match
2025-07-06T04:21:09: 🎯 Knowledge base search for "Can you help troubleshooting": 10 results scored
2025-07-06T04:21:09:    Top result: "Warning (from English_WEB_WAC_1030.pdf)" (score: 0.550)
2025-07-06T04:21:09: 📄 Document search for "Can you help troubleshooting": 5 results scored
2025-07-06T04:21:09:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:21:09:    Applied modifiers: device_match
2025-07-06T04:21:59: 🎯 Knowledge base search for "Screen lock": 2 results scored
2025-07-06T04:21:59:    Top result: "Set system screen lock in Screen Lock. (from CVTE_LFD_WEB_WAD_EN_240726.pdf)" (score: 0.675)
2025-07-06T04:21:59: 📄 Document search for "Screen lock": 2 results scored
2025-07-06T04:21:59:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:21:59:    Applied modifiers: device_match
2025-07-06T04:23:45: 📄 Document search for "Wake on lan": 2 results scored
2025-07-06T04:23:45:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:23:45:    Applied modifiers: device_match
2025-07-06T04:23:45: 🎯 Knowledge base search for "Wake on lan": 3 results scored
2025-07-06T04:23:45:    Top result: "Start up & Shut down  48 (from English_WEB_WAC_1030.pdf)" (score: 0.670)
2025-07-06T04:25:40: 🎯 Knowledge base search for "How do I factor reset the model": 10 results scored
2025-07-06T04:25:40:    Top result: "Connect (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.658)
2025-07-06T04:25:40: 📄 Document search for "How do I factor reset the model": 4 results scored
2025-07-06T04:25:40:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:25:40:    Applied modifiers: device_match
2025-07-06T04:26:16: User connected: MlysvMYMMYVSZuRyAAAH
2025-07-06T04:27:01: User disconnected: MlysvMYMMYVSZuRyAAAH
2025-07-06T04:31:01: User connected: bIKJlejySrK7g0ASAAAJ
2025-07-06T04:31:10: 📄 Document search for "Screen freeze issue": 5 results scored
2025-07-06T04:31:10:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T04:31:10: 🎯 Knowledge base search for "Screen freeze issue": 10 results scored
2025-07-06T04:31:10:    Top result: "Touch Not Responding" (score: 0.617)
2025-07-06T04:31:46: 🎯 Knowledge base search for "Screen freeze error": 10 results scored
2025-07-06T04:31:46:    Top result: "Touch Not Responding" (score: 0.508)
2025-07-06T04:31:46: 📄 Document search for "Screen freeze error": 5 results scored
2025-07-06T04:31:46:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.658)
2025-07-06T04:32:14: 🎯 Knowledge base search for "It's related to wa65d": 10 results scored
2025-07-06T04:32:14:    Top result: "Samsung WA65D Interactive Display - Corrected Specifications" (score: 0.550)
2025-07-06T04:32:14: 📄 Document search for "It's related to wa65d": 4 results scored
2025-07-06T04:32:14:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.675)
2025-07-06T04:32:49: 🎯 Knowledge base search for "The issue is that the screen freeze functionality isn't working": 10 results scored
2025-07-06T04:32:49:    Top result: "Samsung Interactive Display - Common Troubleshooting Issues" (score: 0.620)
2025-07-06T04:32:49: 📄 Document search for "The issue is that the screen freeze functionality isn't working": 5 results scored
2025-07-06T04:32:49:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.620)
2025-07-06T04:33:27: User disconnected: bIKJlejySrK7g0ASAAAJ
2025-07-06T04:34:02: 🎯 Knowledge base search for "Screen freeze not working": 1 results scored
2025-07-06T04:34:02:    Top result: "screen freeze not working properly " (score: 0.675)
2025-07-06T04:34:02: 📄 Document search for "Screen freeze not working": 5 results scored
2025-07-06T04:34:02:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.662)
2025-07-06T04:34:02:    Applied modifiers: device_match
2025-07-06T04:34:58: User connected: dGLwEDu7Dw-aojLIAAAL
2025-07-06T04:34:59: User disconnected: dGLwEDu7Dw-aojLIAAAL
2025-07-06T04:34:59: User connected: pgMW0BSVMF7ktwydAAAN
2025-07-06T04:35:09: 🎯 Knowledge base search for "Screen freeze not working": 1 results scored
2025-07-06T04:35:09:    Top result: "screen freeze not working properly " (score: 0.675)
2025-07-06T04:35:09: 📄 Document search for "Screen freeze not working": 5 results scored
2025-07-06T04:35:09:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.617)
2025-07-06T04:36:06: User disconnected: pgMW0BSVMF7ktwydAAAN
2025-07-06T04:36:21: 🎯 Knowledge base search for "Screen locked and won't unlock": 10 results scored
2025-07-06T04:36:21:    Top result: "Samsung WA-D Series Interactive Displays - Complete Specifications" (score: 0.532)
2025-07-06T04:36:21:    Applied modifiers: device_match
2025-07-06T04:36:21: 📄 Document search for "Screen locked and won't unlock": 5 results scored
2025-07-06T04:36:21:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.663)
2025-07-06T04:37:43: 📄 Document search for "Ah it's actually happening on my wa65f": 5 results scored
2025-07-06T04:37:43:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.550)
2025-07-06T04:37:43: 🎯 Knowledge base search for "Ah it's actually happening on my wa65f": 10 results scored
2025-07-06T04:37:43:    Top result: "Samsung WA65D Interactive Display - Corrected Specifications" (score: 0.467)
2025-07-06T04:38:45: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:38:45: 🎯 Knowledge base search for "Ah it's actually happening on my wa65f": 10 results scored
2025-07-06T04:38:45:    Top result: "Samsung WA65D Interactive Display - Corrected Specifications" (score: 0.467)
2025-07-06T04:38:45: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:38:45: 📄 Document search for "Ah it's actually happening on my wa65f": 5 results scored
2025-07-06T04:38:45:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.550)
2025-07-06T04:38:57: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:38:57: 🎯 Knowledge base search for "Ah it's actually happening on my wa65f": 10 results scored
2025-07-06T04:38:57:    Top result: "Samsung WA65D Interactive Display - Corrected Specifications" (score: 0.467)
2025-07-06T04:38:57: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:38:57: 📄 Document search for "Ah it's actually happening on my wa65f": 5 results scored
2025-07-06T04:38:57:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.550)
2025-07-06T04:39:57: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:39:57: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:39:57: 📄 Document search for "Ah it's actually happening on my wa65f": 5 results scored
2025-07-06T04:39:57:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.550)
2025-07-06T04:39:57: 🎯 Knowledge base search for "Ah it's actually happening on my wa65f": 10 results scored
2025-07-06T04:39:57:    Top result: "Samsung WA65D Interactive Display - Corrected Specifications" (score: 0.467)
2025-07-06T04:40:22: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:40:22: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:40:22: 📄 Document search for "Screen lock issue on wa65f": 5 results scored
2025-07-06T04:40:22:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:40:22:    Applied modifiers: device_match
2025-07-06T04:40:22: 🎯 Knowledge base search for "Screen lock issue on wa65f": 10 results scored
2025-07-06T04:40:22:    Top result: "Touch Not Responding" (score: 0.575)
2025-07-06T04:40:45: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:40:45: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:40:45: 🎯 Knowledge base search for "Can you help me troubleshooting": 10 results scored
2025-07-06T04:40:45:    Top result: "Warning (from English_WEB_WAC_1030.pdf)" (score: 0.550)
2025-07-06T04:40:45: 📄 Document search for "Can you help me troubleshooting": 5 results scored
2025-07-06T04:40:45:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:40:45:    Applied modifiers: device_match
2025-07-06T04:40:52: User connected: FXkrS_y2hKbOnnhBAAAQ
2025-07-06T04:40:54: User disconnected: FXkrS_y2hKbOnnhBAAAQ
2025-07-06T04:40:54: User connected: bKAh-yrAOgeRSjXrAAAS
2025-07-06T04:41:03: User disconnected: bKAh-yrAOgeRSjXrAAAS
2025-07-06T04:41:45: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:41:45: 📄 Document search for "Can you help me troubleshooting": 5 results scored
2025-07-06T04:41:45:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:41:45:    Applied modifiers: device_match
2025-07-06T04:41:45: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:41:45: 🎯 Knowledge base search for "Can you help me troubleshooting": 10 results scored
2025-07-06T04:41:45:    Top result: "Warning (from English_WEB_WAC_1030.pdf)" (score: 0.550)
2025-07-06T04:42:20: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:42:20: 📄 Document search for "I need help troubleshooting": 5 results scored
2025-07-06T04:42:20:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:42:20:    Applied modifiers: device_match
2025-07-06T04:42:21: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:42:21: 🎯 Knowledge base search for "I need help troubleshooting": 10 results scored
2025-07-06T04:42:21:    Top result: "Tips for eye health (from English_WEB_WAC_1030.pdf)" (score: 0.617)
2025-07-06T04:42:55: User connected: bCwQQ322CN2NoLZ_AAAX
2025-07-06T04:42:57: User disconnected: bCwQQ322CN2NoLZ_AAAX
2025-07-06T04:42:57: User connected: MvFkLp-1umXgoOQUAAAZ
2025-07-06T04:43:08: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:43:08: 🎯 Knowledge base search for "I need help": 10 results scored
2025-07-06T04:43:08:    Top result: "Tips for eye health (from English_WEB_WAC_1030.pdf)" (score: 0.675)
2025-07-06T04:43:08: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:43:08: 📄 Document search for "I need help": 4 results scored
2025-07-06T04:43:08:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T04:43:28: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:43:28: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:43:28: 🎯 Knowledge base search for "Can you help me troubleshooting": 10 results scored
2025-07-06T04:43:28:    Top result: "Warning (from English_WEB_WAC_1030.pdf)" (score: 0.550)
2025-07-06T04:43:28: 📄 Document search for "Can you help me troubleshooting": 5 results scored
2025-07-06T04:43:28:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T04:43:51: User disconnected: MvFkLp-1umXgoOQUAAAZ
2025-07-06T04:43:54: User connected: cA48cHpf-rZlpl2lAAAb
2025-07-06T04:44:04: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:44:04: 📄 Document search for "Can you help": 5 results scored
2025-07-06T04:44:04:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T04:44:04: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:44:05: 🎯 Knowledge base search for "Can you help": 10 results scored
2025-07-06T04:44:05:    Top result: "Warning (from English_WEB_WAC_1030.pdf)" (score: 0.613)
2025-07-06T04:44:18: User disconnected: cA48cHpf-rZlpl2lAAAb
2025-07-06T04:44:46: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:44:47: 🎯 Knowledge base search for "Screen lock issue": 10 results scored
2025-07-06T04:44:47:    Top result: "Touch Not Responding" (score: 0.617)
2025-07-06T04:44:47: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:44:47: 📄 Document search for "Screen lock issue": 5 results scored
2025-07-06T04:44:47:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:44:47:    Applied modifiers: device_match
2025-07-06T04:45:21: User connected: IXEWtyPy70L9oXUCAAAe
2025-07-06T04:45:29: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:45:29: 🎯 Knowledge base search for "Need support": 10 results scored
2025-07-06T04:45:29:    Top result: "The modem port on the (from English_WEB_WAC_1030.pdf)" (score: 0.675)
2025-07-06T04:45:29: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:45:29: 📄 Document search for "Need support": 5 results scored
2025-07-06T04:45:29:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T04:45:46: Vector document search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:45:46: 📄 Document search for "Screen lock issue": 5 results scored
2025-07-06T04:45:46:    Top result: "CVTE_LFD_WEB_WAD_EN_240726.pdf" (score: 0.720)
2025-07-06T04:45:46:    Applied modifiers: device_match
2025-07-06T04:45:46: Vector search failed, falling back to text search: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-06T04:45:46: 🎯 Knowledge base search for "Screen lock issue": 10 results scored
2025-07-06T04:45:46:    Top result: "Touch Not Responding" (score: 0.617)
2025-07-06T04:51:23: User disconnected: IXEWtyPy70L9oXUCAAAe
2025-07-06T04:55:40: 
Shutting down gracefully...
2025-07-06T04:55:57: 🚀 AI Product Support Agent running on port 3000
2025-07-06T04:55:58: 🌐 Web interface: http://localhost:3000
2025-07-06T04:55:58: 🤖 Telegram bot: Active
2025-07-06T04:55:58: 📊 Database: Connected
2025-07-06T04:55:58: 🧠 OpenAI: Connected
2025-07-06T04:55:58: Database connection successful
2025-07-06T04:55:58: Webhook set successfully
2025-07-06T04:55:58: Telegram bot webhook set up
2025-07-06T04:55:58: All services initialized successfully
2025-07-06T04:56:01: User connected: iY8WGnPhFcGK_yC7AAAB
2025-07-06T04:56:01: User connected: _P_CB9NstKFrXk46AAAD
2025-07-06T04:56:04: User connected: yt6IfJH8tfmRffezAAAF
2025-07-06T04:56:09: 🎯 Knowledge base search for "Screen freeze": 1 results scored
2025-07-06T04:56:09:    Top result: "screen freeze not working properly " (score: 0.675)
2025-07-06T04:56:09: 📄 Document search for "Screen freeze": 5 results scored
2025-07-06T04:56:09:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T04:56:22: User disconnected: _P_CB9NstKFrXk46AAAD
2025-07-06T04:57:29: User connected: fv4WDcFJTJb0J5JcAAAH
2025-07-06T04:57:38: 🎯 Knowledge base search for "Wake on lan": 3 results scored
2025-07-06T04:57:38:    Top result: "Start up & Shut down  48 (from English_WEB_WAC_1030.pdf)" (score: 0.670)
2025-07-06T04:57:39: 📄 Document search for "Wake on lan": 2 results scored
2025-07-06T04:57:39:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.675)
2025-07-06T04:58:13: User disconnected: fv4WDcFJTJb0J5JcAAAH
2025-07-06T04:59:53: User connected: S9c_1Q8ubHwoBVXZAAAM
2025-07-06T05:00:05: 🎯 Knowledge base search for "Any other common issues": 10 results scored
2025-07-06T05:00:05:    Top result: "Using the wall mount kit (from English_WEB_WAC_1030.pdf)" (score: 0.606)
2025-07-06T05:00:05: 📄 Document search for "Any other common issues": 5 results scored
2025-07-06T05:00:05:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.675)
2025-07-06T05:00:23: User disconnected: S9c_1Q8ubHwoBVXZAAAM
2025-07-06T05:00:43: User connected: nC0-P7jYEQw0jqPnAAAO
2025-07-06T05:00:55: User disconnected: nC0-P7jYEQw0jqPnAAAO
2025-07-06T05:04:26: User disconnected: iY8WGnPhFcGK_yC7AAAB
2025-07-06T05:04:26: User disconnected: yt6IfJH8tfmRffezAAAF
2025-07-06T05:05:51: 
Shutting down gracefully...
2025-07-06T05:05:53: 🚀 AI Product Support Agent running on port 3000
2025-07-06T05:05:53: 🌐 Web interface: http://localhost:3000
2025-07-06T05:05:53: 🤖 Telegram bot: Active
2025-07-06T05:05:53: 📊 Database: Connected
2025-07-06T05:05:53: 🧠 OpenAI: Connected
2025-07-06T05:05:53: Database connection successful
2025-07-06T05:05:54: Webhook set successfully
2025-07-06T05:05:54: Telegram bot webhook set up
2025-07-06T05:05:54: All services initialized successfully
2025-07-06T05:07:25: 🎯 Knowledge base search for "Screen freeze": 1 results scored
2025-07-06T05:07:25:    Top result: "screen freeze not working properly " (score: 0.679)
2025-07-06T05:07:25: 📄 Document search for "Screen freeze": 5 results scored
2025-07-06T05:07:25:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:07:25:    Applied modifiers: device_match
2025-07-06T05:08:03: 🎯 Knowledge base search for "Screen freeze": 1 results scored
2025-07-06T05:08:03:    Top result: "screen freeze not working properly " (score: 0.679)
2025-07-06T05:08:03: 📄 Document search for "Screen freeze": 5 results scored
2025-07-06T05:08:03:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:08:03:    Applied modifiers: device_match
2025-07-06T05:09:06: User connected: Y3JdJei0blKeYNsmAAAB
2025-07-06T05:09:50: User disconnected: Y3JdJei0blKeYNsmAAAB
2025-07-06T05:09:55: 🎯 Knowledge base search for "Anynet+": 9 results scored
2025-07-06T05:09:55:    Top result: "The message “Disconnecting Anynet+ device...” (from English_WEB_WAC_1030.pdf)" (score: 0.684)
2025-07-06T05:09:55: 📄 Document search for "Anynet+": 3 results scored
2025-07-06T05:09:55:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.701)
2025-07-06T05:11:27: 🎯 Knowledge base search for "Picture in picture": 10 results scored
2025-07-06T05:11:27:    Top result: "Source window (HDMI) issue (from English_WEB_WAC_1030.pdf)" (score: 0.696)
2025-07-06T05:11:27: 📄 Document search for "Picture in picture": 4 results scored
2025-07-06T05:11:27:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:11:27:    Applied modifiers: device_match
2025-07-06T05:12:36: 📄 Document search for "Mind map": 3 results scored
2025-07-06T05:12:36:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:12:36:    Applied modifiers: device_match
2025-07-06T05:12:36: 🎯 Knowledge base search for "Mind map": 5 results scored
2025-07-06T05:12:36:    Top result: "Grid (from English_WEB_WAC_1030.pdf)" (score: 0.679)
2025-07-06T05:13:17: 🎯 Knowledge base search for "Ruler": 1 results scored
2025-07-06T05:13:17:    Top result: "Ruler (from English_WEB_WAC_1030.pdf)" (score: 0.670)
2025-07-06T05:13:17: 📄 Document search for "Ruler": 3 results scored
2025-07-06T05:13:17:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:13:17:    Applied modifiers: device_match
2025-07-06T05:13:47: 🎯 Knowledge base search for "Browser": 10 results scored
2025-07-06T05:13:47:    Top result: "Samsung WA-F Series Interactive Displays - Latest Models" (score: 0.729)
2025-07-06T05:13:47:    Applied modifiers: device_match
2025-07-06T05:13:48: 📄 Document search for "Browser": 4 results scored
2025-07-06T05:13:48:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:13:48:    Applied modifiers: device_match
2025-07-06T05:14:11: 🎯 Knowledge base search for "Software update": 1 results scored
2025-07-06T05:14:11:    Top result: "Support (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.675)
2025-07-06T05:14:11: 📄 Document search for "Software update": 3 results scored
2025-07-06T05:14:11:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:14:11:    Applied modifiers: device_match
2025-07-06T05:15:11: 🎯 Knowledge base search for "Software update": 1 results scored
2025-07-06T05:15:11:    Top result: "Support (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.675)
2025-07-06T05:15:11: 📄 Document search for "Software update": 3 results scored
2025-07-06T05:15:11:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:15:11:    Applied modifiers: device_match
2025-07-06T05:24:46: User connected: QN-CYQyN3zWp-59IAAAD
2025-07-06T05:25:01: User disconnected: QN-CYQyN3zWp-59IAAAD
2025-07-06T05:26:23: 🎯 Knowledge base search for "Kiosk mode": 2 results scored
2025-07-06T05:26:23:    Top result: "Kiosk Mode (from English_WEB_WAC_1030.pdf)" (score: 0.679)
2025-07-06T05:26:23: 📄 Document search for "Kiosk mode": 1 results scored
2025-07-06T05:26:23:    Top result: "English_WEB_WAC_1030.pdf" (score: 0.701)
2025-07-06T05:27:46: 🎯 Knowledge base search for "Safety measures": 8 results scored
2025-07-06T05:27:46:    Top result: "System Settings (from CVTE_LFD_WEB_WAD_EN_240726.pdf)" (score: 0.554)
2025-07-06T05:27:47: 📄 Document search for "Safety measures": 4 results scored
2025-07-06T05:27:47:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.683)
2025-07-06T05:27:47:    Applied modifiers: device_match
2025-07-06T05:28:25: 🎯 Knowledge base search for "Switching source": 10 results scored
2025-07-06T05:28:25:    Top result: "Power Off (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.688)
2025-07-06T05:28:25: 📄 Document search for "Switching source": 5 results scored
2025-07-06T05:28:25:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:28:25:    Applied modifiers: device_match
2025-07-06T05:29:44: 🎯 Knowledge base search for "Wallpaper": 5 results scored
2025-07-06T05:29:44:    Top result: "System Settings (from CVTE_LFD_WEB_WAD_EN_240726.pdf)" (score: 0.679)
2025-07-06T05:29:44: 📄 Document search for "Wallpaper": 3 results scored
2025-07-06T05:29:44:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:29:44:    Applied modifiers: device_match
2025-07-06T05:30:09: 🎯 Knowledge base search for "Amd": 1 results scored
2025-07-06T05:30:09:    Top result: "screen freeze not working properly " (score: 0.679)
2025-07-06T05:30:28: User connected: jOlZNvwmv_ocxPcFAAAF
2025-07-06T05:30:29: User disconnected: jOlZNvwmv_ocxPcFAAAF
2025-07-06T05:31:09: 🎯 Knowledge base search for "Amd": 1 results scored
2025-07-06T05:31:09:    Top result: "screen freeze not working properly " (score: 0.679)
2025-07-06T05:31:32: 🎯 Knowledge base search for "Settings on AMD device": 10 results scored
2025-07-06T05:31:32:    Top result: "Samsung Interactive Display - Common Troubleshooting Issues" (score: 0.625)
2025-07-06T05:31:32: 📄 Document search for "Settings on AMD device": 5 results scored
2025-07-06T05:31:32:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.687)
2025-07-06T05:31:32:    Applied modifiers: device_match
2025-07-06T05:32:32: 🎯 Knowledge base search for "Settings on AMD device": 10 results scored
2025-07-06T05:32:32:    Top result: "Samsung Interactive Display - Common Troubleshooting Issues" (score: 0.625)
2025-07-06T05:32:32: 📄 Document search for "Settings on AMD device": 5 results scored
2025-07-06T05:32:32:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.687)
2025-07-06T05:32:32:    Applied modifiers: device_match
2025-07-06T05:32:35: 📄 Document search for "Screen freeze": 5 results scored
2025-07-06T05:32:35:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:32:35:    Applied modifiers: device_match
2025-07-06T05:32:35: 🎯 Knowledge base search for "Screen freeze": 1 results scored
2025-07-06T05:32:35:    Top result: "screen freeze not working properly " (score: 0.679)
2025-07-06T05:58:27: 🎯 Knowledge base search for "Which models have android?": 10 results scored
2025-07-06T05:58:27:    Top result: "Samsung WA-F Series Interactive Displays - Latest Models" (score: 0.729)
2025-07-06T05:58:27:    Applied modifiers: device_match
2025-07-06T05:58:27: 📄 Document search for "Which models have android?": 5 results scored
2025-07-06T05:58:27:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:58:27:    Applied modifiers: device_match
2025-07-06T05:59:04: 🎯 Knowledge base search for "What software does wm55b use?": 10 results scored
2025-07-06T05:59:04:    Top result: "Samsung Interactive Display - Common Troubleshooting Issues" (score: 0.625)
2025-07-06T05:59:04: 📄 Document search for "What software does wm55b use?": 5 results scored
2025-07-06T05:59:04:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.701)
2025-07-06T05:59:26: 🎯 Knowledge base search for "Are there any accessories?": 10 results scored
2025-07-06T05:59:26:    Top result: "Children may become injured or seriously harmed. (from English_WEB_WAC_1030.pdf)" (score: 0.550)
2025-07-06T05:59:26: 📄 Document search for "Are there any accessories?": 5 results scored
2025-07-06T05:59:26:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T05:59:26:    Applied modifiers: device_match
2025-07-06T06:00:26: 📄 Document search for "Are there any accessories?": 5 results scored
2025-07-06T06:00:26:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T06:00:26:    Applied modifiers: device_match
2025-07-06T06:00:26: 🎯 Knowledge base search for "Are there any accessories?": 10 results scored
2025-07-06T06:00:26:    Top result: "Children may become injured or seriously harmed. (from English_WEB_WAC_1030.pdf)" (score: 0.550)
2025-07-06T06:01:05: 🎯 Knowledge base search for "I asked about wm55b": 10 results scored
2025-07-06T06:01:05:    Top result: "System Update (from English_WEB_WAC_1030.pdf)" (score: 0.521)
2025-07-06T06:01:05: 📄 Document search for "I asked about wm55b": 4 results scored
2025-07-06T06:01:05:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.701)
2025-07-06T06:01:19: 🎯 Knowledge base search for "Connectivity tray": 10 results scored
2025-07-06T06:01:19:    Top result: "Samsung WM-B Series Flip Displays - Corrected Specifications" (score: 0.684)
2025-07-06T06:01:20: 📄 Document search for "Connectivity tray": 3 results scored
2025-07-06T06:01:20:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.701)
2025-07-06T14:53:31: 🎯 Knowledge base search for "Can you provide manuals for these?": 10 results scored
2025-07-06T14:53:31:    Top result: "Power button & (from English_WEB_WAC_1030.pdf)" (score: 0.521)
2025-07-06T14:53:31: 📄 Document search for "Can you provide manuals for these?": 5 results scored
2025-07-06T14:53:31:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.729)
2025-07-06T14:53:31:    Applied modifiers: device_match
2025-07-06T14:55:55: 🎯 Knowledge base search for "There isn't a touch lock button": 10 results scored
2025-07-06T14:55:55:    Top result: "When two pens are used, display has difficulty (from English_WEB_WAC_1030.pdf)" (score: 0.496)
2025-07-06T14:55:55: 📄 Document search for "There isn't a touch lock button": 5 results scored
2025-07-06T14:55:55:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.733)
2025-07-06T14:55:55:    Applied modifiers: device_match
2025-07-06T14:57:48: 📄 Document search for "Nothing happens it's still locked": 4 results scored
2025-07-06T14:57:48:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.688)
2025-07-06T14:57:49: 🎯 Knowledge base search for "Nothing happens it's still locked": 10 results scored
2025-07-06T14:57:49:    Top result: "Source window (HDMI) issue (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.500)
2025-07-06T14:58:49: 🎯 Knowledge base search for "Nothing happens it's still locked": 10 results scored
2025-07-06T14:58:49:    Top result: "Source window (HDMI) issue (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.500)
2025-07-06T14:58:49: 📄 Document search for "Nothing happens it's still locked": 4 results scored
2025-07-06T14:58:49:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.688)
2025-07-06T14:58:53: 🎯 Knowledge base search for "Nothing happens it's still locked": 10 results scored
2025-07-06T14:58:53:    Top result: "Source window (HDMI) issue (from BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf)" (score: 0.500)
2025-07-06T14:58:53: 📄 Document search for "Nothing happens it's still locked": 4 results scored
2025-07-06T14:58:53:    Top result: "BN81-22636B-05_WEB_WMB_EU_ENG_240614.0.pdf" (score: 0.688)
2025-07-06T14:59:39: 🎯 Knowledge base search for "Screen locked": 10 results scored
2025-07-06T14:59:39:    Top result: "Samsung WA-F Series Interactive Displays - Latest Models" (score: 0.604)
2025-07-06T14:59:39:    Applied modifiers: device_match
2025-07-06T14:59:39: 📄 Document search for "Screen locked": 5 results scored
2025-07-06T14:59:39:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.746)
2025-07-06T14:59:39:    Applied modifiers: device_match
2025-07-06T16:43:50: 🎯 Knowledge base search for "I'm having issues with screen lock on wa65f": 10 results scored
2025-07-06T16:43:50:    Top result: "Samsung Interactive Display - Common Troubleshooting Issues" (score: 0.579)
2025-07-06T16:43:50: 📄 Document search for "I'm having issues with screen lock on wa65f": 5 results scored
2025-07-06T16:43:50:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.766)
2025-07-06T16:43:50:    Applied modifiers: device_match
2025-07-06T16:44:50: 🎯 Knowledge base search for "I'm having issues with screen lock on wa65f": 10 results scored
2025-07-06T16:44:50:    Top result: "Samsung Interactive Display - Common Troubleshooting Issues" (score: 0.579)
2025-07-06T16:44:50: 📄 Document search for "I'm having issues with screen lock on wa65f": 5 results scored
2025-07-06T16:44:50:    Top result: "WAF_Web_Manual_English_24.11.05.pdf" (score: 0.766)
2025-07-06T16:44:50:    Applied modifiers: device_match
2025-07-06T16:53:04: 
Shutting down gracefully...
2025-07-06T16:53:06: 🚀 AI Product Support Agent running on port 3000
2025-07-06T16:53:06: 🌐 Web interface: http://localhost:3000
2025-07-06T16:53:06: 🤖 Telegram bot: Active
2025-07-06T16:53:06: 📊 Database: Connected
2025-07-06T16:53:06: 🧠 OpenAI: Connected
2025-07-06T16:53:06: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T15:53:06.312Z"}
2025-07-06T16:53:06: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T15:53:06.319Z"}
2025-07-06T16:53:06: Database connection successful
2025-07-06T16:53:06: Webhook set successfully
2025-07-06T16:53:06: Telegram bot webhook set up
2025-07-06T16:53:06: All services initialized successfully
2025-07-06T16:54:23: [32minfo[39m: ✅ Weaviate search: 20 KB + 0 docs for "*" {"service":"weaviate-service","timestamp":"2025-07-06T15:54:23.162Z"}
2025-07-06T16:54:33: [32minfo[39m: ✅ Weaviate search: 20 KB + 1 docs for "*" {"service":"weaviate-service","timestamp":"2025-07-06T15:54:33.684Z"}
2025-07-06T16:54:39: [32minfo[39m: ✅ Weaviate search: 20 KB + 1 docs for "*" {"service":"weaviate-service","timestamp":"2025-07-06T15:54:39.154Z"}
2025-07-06T16:54:49: [32minfo[39m: ✅ Weaviate search: 20 KB + 0 docs for "*" {"service":"weaviate-service","timestamp":"2025-07-06T15:54:49.498Z"}
2025-07-06T16:54:52: [32minfo[39m: ✅ Weaviate search: 20 KB + 1 docs for "*" {"service":"weaviate-service","timestamp":"2025-07-06T15:54:52.155Z"}
2025-07-06T16:54:57: [32minfo[39m: ✅ Weaviate search: 20 KB + 0 docs for "*" {"service":"weaviate-service","timestamp":"2025-07-06T15:54:57.133Z"}
2025-07-06T16:54:58: [32minfo[39m: ✅ Weaviate search: 20 KB + 1 docs for "*" {"service":"weaviate-service","timestamp":"2025-07-06T15:54:58.379Z"}
2025-07-06T16:55:17: [32minfo[39m: ✅ Weaviate search: 10 KB + 0 docs for "screen lock" {"service":"weaviate-service","timestamp":"2025-07-06T15:55:17.206Z"}
2025-07-06T16:58:12: 
Shutting down gracefully...
2025-07-06T16:58:14: 🚀 AI Product Support Agent running on port 3000
2025-07-06T16:58:14: 🌐 Web interface: http://localhost:3000
2025-07-06T16:58:14: 🤖 Telegram bot: Active
2025-07-06T16:58:14: 📊 Database: Connected
2025-07-06T16:58:14: 🧠 OpenAI: Connected
2025-07-06T16:58:14: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T15:58:14.040Z"}
2025-07-06T16:58:14: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T15:58:14.044Z"}
2025-07-06T16:58:14: ✅ Telegram bot: Hybrid search initialized
2025-07-06T16:58:14: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T15:58:14.051Z"}
2025-07-06T16:58:14: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T15:58:14.053Z"}
2025-07-06T16:58:14: Database connection successful
2025-07-06T16:58:14: Webhook set successfully
2025-07-06T16:58:14: Telegram bot webhook set up
2025-07-06T16:58:14: All services initialized successfully
2025-07-06T17:04:55: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs for "I'm using a waf series and the screen is locked and I can't seem to unlock it" {"service":"weaviate-service","timestamp":"2025-07-06T16:04:55.439Z"}
2025-07-06T17:05:55: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs for "I'm using a waf series and the screen is locked and I can't seem to unlock it" {"service":"weaviate-service","timestamp":"2025-07-06T16:05:55.529Z"}
2025-07-06T17:07:12: [32minfo[39m: ✅ Weaviate search: 5 KB + 1 docs for "Show the manual" {"service":"weaviate-service","timestamp":"2025-07-06T16:07:12.642Z"}
2025-07-06T17:08:12: [32minfo[39m: ✅ Weaviate search: 5 KB + 1 docs for "Show the manual" {"service":"weaviate-service","timestamp":"2025-07-06T16:08:12.819Z"}
2025-07-06T17:09:10: [32minfo[39m: ✅ Weaviate search: 5 KB + 1 docs for "What other manuals are available?" {"service":"weaviate-service","timestamp":"2025-07-06T16:09:10.210Z"}
2025-07-06T17:54:33: 
Shutting down gracefully...
2025-07-06T17:54:36: 🚀 AI Product Support Agent running on port 3000
2025-07-06T17:54:36: 🌐 Web interface: http://localhost:3000
2025-07-06T17:54:36: 🤖 Telegram bot: Active
2025-07-06T17:54:36: 📊 Database: Connected
2025-07-06T17:54:36: 🧠 OpenAI: Connected
2025-07-06T17:54:36: ✅ Redis connected
2025-07-06T17:54:36: ✅ Redis connected
2025-07-06T17:54:36: 🔥 Warming up cache...
2025-07-06T17:54:36: 🔥 Warming up cache...
2025-07-06T17:54:36: ✅ Cache warmed up successfully
2025-07-06T17:54:36: ✅ Cache warmed up successfully
2025-07-06T17:54:36: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T16:54:36.360Z"}
2025-07-06T17:54:36: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T16:54:36.366Z"}
2025-07-06T17:54:36: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T16:54:36.420Z"}
2025-07-06T17:54:36: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T16:54:36.421Z"}
2025-07-06T17:54:36: ✅ Telegram bot: Hybrid search initialized
2025-07-06T17:54:36: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T16:54:36.429Z"}
2025-07-06T17:54:36: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T16:54:36.430Z"}
2025-07-06T17:54:36: Database connection successful
2025-07-06T17:54:36: Webhook set successfully
2025-07-06T17:54:36: Telegram bot webhook set up
2025-07-06T17:54:36: All services initialized successfully
2025-07-06T18:00:01: [32minfo[39m: ✅ Weaviate search: 5 KB + 1 docs for "Can you help me resolve a screen lock issue?" {"service":"weaviate-service","timestamp":"2025-07-06T17:00:01.000Z"}
2025-07-06T18:00:01: 💾 Cached search results for: "Can you help me resolve a screen lock issue?"
2025-07-06T18:00:01: 🔍 Search completed in 1208ms
2025-07-06T18:08:41: ⚡ Cache hit for: "Can you help me resolve a screen lock issue?"
2025-07-06T18:08:41: [32minfo[39m: ⚡ Cache hit for search: "Can you help me resolve a screen lock issue?" {"service":"weaviate-service","timestamp":"2025-07-06T17:08:41.112Z"}
2025-07-06T18:08:41: 🔍 Search completed in 9ms
2025-07-06T18:10:40: [32minfo[39m: ✅ Weaviate search: 5 KB + 1 docs for "I'm having an issue with the screen lock" {"service":"weaviate-service","timestamp":"2025-07-06T17:10:40.856Z"}
2025-07-06T18:10:40: 💾 Cached search results for: "I'm having an issue with the screen lock"
2025-07-06T18:10:40: 🔍 Search completed in 827ms
2025-07-06T18:11:51: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs for "WA65F" {"service":"weaviate-service","timestamp":"2025-07-06T17:11:51.877Z"}
2025-07-06T18:11:51: 🔍 Search completed in 743ms
2025-07-06T18:11:56: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs for "What models use android?" {"service":"weaviate-service","timestamp":"2025-07-06T17:11:56.985Z"}
2025-07-06T18:11:56: 🔍 Search completed in 497ms
2025-07-06T18:17:33: [32minfo[39m: ✅ Weaviate search: 5 KB + 1 docs for "Hey" {"service":"weaviate-service","timestamp":"2025-07-06T17:17:33.779Z"}
2025-07-06T18:17:33: 💾 Cached search results for: "Hey"
2025-07-06T18:17:33: 🔍 Search completed in 686ms
2025-07-06T18:21:23: 
Shutting down gracefully...
2025-07-06T18:21:32: 🚀 AI Product Support Agent running on port 3000
2025-07-06T18:21:32: 🌐 Web interface: http://localhost:3000
2025-07-06T18:21:32: 🤖 Telegram bot: Active
2025-07-06T18:21:32: 📊 Database: Connected
2025-07-06T18:21:32: 🧠 OpenAI: Connected
2025-07-06T18:21:32: ✅ Redis connected
2025-07-06T18:21:32: ✅ Redis connected
2025-07-06T18:21:32: 🔥 Warming up cache...
2025-07-06T18:21:32: 🔥 Warming up cache...
2025-07-06T18:21:32: ✅ Cache warmed up successfully
2025-07-06T18:21:32: ✅ Cache warmed up successfully
2025-07-06T18:21:32: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T17:21:32.145Z"}
2025-07-06T18:21:32: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T17:21:32.152Z"}
2025-07-06T18:21:32: Database connection successful
2025-07-06T18:21:32: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T17:21:32.229Z"}
2025-07-06T18:21:32: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T17:21:32.231Z"}
2025-07-06T18:21:32: ✅ Telegram bot: Hybrid search initialized
2025-07-06T18:21:32: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T17:21:32.236Z"}
2025-07-06T18:21:32: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T17:21:32.237Z"}
2025-07-06T18:21:32: Webhook set successfully
2025-07-06T18:21:32: Telegram bot webhook set up
2025-07-06T18:21:32: All services initialized successfully
2025-07-06T18:30:05: 🔍 Detected product: WA65F (Samsung)
2025-07-06T18:30:05: ✅ Created user device tracking for 5560551341: WA65F
2025-07-06T18:30:08: [32minfo[39m: ✅ Weaviate search: 1 KB + 1 docs + 0 structured for "Wa65f" {"service":"weaviate-service","timestamp":"2025-07-06T17:30:08.557Z"}
2025-07-06T18:30:08: 💾 Cached search results for: "Wa65f"
2025-07-06T18:30:08: 🔍 Search completed in 829ms
2025-07-06T18:34:34: 🔍 Detected product: WA65F (Samsung)
2025-07-06T18:34:34: ✅ Updated user device tracking for 5560551341: WA65F
2025-07-06T18:34:35: ✅ Created support issue for user 5560551341: General Support
2025-07-06T18:34:35: 🎟️ Created support issue: General Support - General Question
2025-07-06T18:34:36: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "My wa65f is locked" {"service":"weaviate-service","timestamp":"2025-07-06T17:34:36.951Z"}
2025-07-06T18:34:36: 🔍 Search completed in 850ms
2025-07-06T18:38:20: 🔍 Detected product: WA65F (Samsung)
2025-07-06T18:38:20: ✅ Updated user device tracking for 5560551341: WA65F
2025-07-06T18:38:54: ✅ Created support issue for user 5560551341: General Support
2025-07-06T18:38:54: 🎟️ Created support issue: General Support - General Question
2025-07-06T18:38:55: [32minfo[39m: ✅ Weaviate search: 5 KB + 1 docs + 0 structured for "I need technical support" {"service":"weaviate-service","timestamp":"2025-07-06T17:38:55.507Z"}
2025-07-06T18:38:55: 💾 Cached search results for: "I need technical support"
2025-07-06T18:38:55: 🔍 Search completed in 1041ms
2025-07-06T18:39:37: 
Shutting down gracefully...
2025-07-06T18:39:44: 🚀 AI Product Support Agent running on port 3000
2025-07-06T18:39:44: 🌐 Web interface: http://localhost:3000
2025-07-06T18:39:44: 🤖 Telegram bot: Active
2025-07-06T18:39:44: 📊 Database: Connected
2025-07-06T18:39:44: 🧠 OpenAI: Connected
2025-07-06T18:39:44: ✅ Redis connected
2025-07-06T18:39:44: ✅ Redis connected
2025-07-06T18:39:44: 🔥 Warming up cache...
2025-07-06T18:39:44: 🔥 Warming up cache...
2025-07-06T18:39:44: ✅ Cache warmed up successfully
2025-07-06T18:39:44: ✅ Cache warmed up successfully
2025-07-06T18:39:44: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T17:39:44.809Z"}
2025-07-06T18:39:44: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T17:39:44.817Z"}
2025-07-06T18:39:44: Database connection successful
2025-07-06T18:39:44: Starting Telegram bot in polling mode...
2025-07-06T18:39:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T17:39:44.893Z"}
2025-07-06T18:39:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T17:39:44.894Z"}
2025-07-06T18:39:44: ✅ Telegram bot: Hybrid search initialized
2025-07-06T18:39:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T17:39:44.899Z"}
2025-07-06T18:39:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T17:39:44.900Z"}
2025-07-06T18:40:14: Bot launch timed out, but server will continue running...
2025-07-06T18:40:14: All services initialized successfully
2025-07-06T18:42:09: 📨 Webhook received: 2025-07-06T17:42:09.271Z
2025-07-06T18:42:09: 📨 Request body: {
  "test": "webhook"
}
2025-07-06T18:42:50: 📨 Webhook received: 2025-07-06T17:42:50.516Z
2025-07-06T18:42:50: 📨 Request body: {
  "update_id": 706612681,
  "message": {
    "message_id": 494,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751823770,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T18:42:54: 📨 Webhook received: 2025-07-06T17:42:54.627Z
2025-07-06T18:42:54: 📨 Request body: {
  "update_id": 706612682,
  "message": {
    "message_id": 496,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751823774,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T18:43:13: 📨 Webhook received: 2025-07-06T17:43:13.672Z
2025-07-06T18:43:13: 📨 Request body: {
  "update_id": 706612683,
  "message": {
    "message_id": 498,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751823793,
    "text": "I need help with the screen lock on wa65f I can't seem to unlock it"
  }
}
2025-07-06T18:43:13: 🔍 Detected product: WA65F (Samsung)
2025-07-06T18:43:13: ✅ Updated user device tracking for 5560551341: WA65F
2025-07-06T18:44:04: 📨 Webhook received: 2025-07-06T17:44:04.179Z
2025-07-06T18:44:04: 📨 Request body: {
  "update_id": 123456789,
  "message": {
    "message_id": 123,
    "from": {
      "id": 987654321,
      "is_bot": false,
      "first_name": "Test",
      "username": "testuser"
    },
    "chat": {
      "id": 987654321,
      "first_name": "Test",
      "username": "testuser",
      "type": "private"
    },
    "date": 1625097600,
    "text": "test message"
  }
}
2025-07-06T18:44:06: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "test message" {"service":"weaviate-service","timestamp":"2025-07-06T17:44:06.378Z"}
2025-07-06T18:44:06: 💾 Cached search results for: "test message"
2025-07-06T18:44:25: 
Shutting down gracefully...
2025-07-06T18:44:33: 🚀 AI Product Support Agent running on port 3000
2025-07-06T18:44:33: 🌐 Web interface: http://localhost:3000
2025-07-06T18:44:33: 🤖 Telegram bot: Active
2025-07-06T18:44:33: 📊 Database: Connected
2025-07-06T18:44:33: 🧠 OpenAI: Connected
2025-07-06T18:44:33: ✅ Redis connected
2025-07-06T18:44:33: ✅ Redis connected
2025-07-06T18:44:33: 🔥 Warming up cache...
2025-07-06T18:44:33: 🔥 Warming up cache...
2025-07-06T18:44:33: Database connection successful
2025-07-06T18:44:33: ✅ Cache warmed up successfully
2025-07-06T18:44:33: ✅ Cache warmed up successfully
2025-07-06T18:44:33: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T17:44:33.732Z"}
2025-07-06T18:44:33: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T17:44:33.741Z"}
2025-07-06T18:44:33: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T17:44:33.769Z"}
2025-07-06T18:44:33: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T17:44:33.772Z"}
2025-07-06T18:44:33: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T17:44:33.778Z"}
2025-07-06T18:44:33: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T17:44:33.780Z"}
2025-07-06T18:44:33: ✅ Telegram bot: Hybrid search initialized
2025-07-06T18:44:33: Webhook set successfully
2025-07-06T18:44:33: Telegram bot webhook set up
2025-07-06T18:44:33: All services initialized successfully
2025-07-06T18:45:22: 📨 Webhook received: 2025-07-06T17:45:22.343Z
2025-07-06T18:45:22: 📨 Request body: {
  "update_id": 706612684,
  "message": {
    "message_id": 499,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751823922,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T18:45:26: 📨 Webhook received: 2025-07-06T17:45:26.294Z
2025-07-06T18:45:26: 📨 Request body: {
  "update_id": 706612685,
  "message": {
    "message_id": 501,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751823926,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T18:45:32: 📨 Webhook received: 2025-07-06T17:45:32.302Z
2025-07-06T18:45:32: 📨 Request body: {
  "update_id": 706612686,
  "message": {
    "message_id": 503,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751823932,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T18:45:52: 📨 Webhook received: 2025-07-06T17:45:52.882Z
2025-07-06T18:45:52: 📨 Request body: {
  "update_id": 706612687,
  "message": {
    "message_id": 505,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751823952,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T18:46:47: 📨 Webhook received: 2025-07-06T17:46:47.530Z
2025-07-06T18:46:47: 📨 Request body: {
  "update_id": 123456790,
  "message": {
    "message_id": 124,
    "from": {
      "id": 987654321,
      "is_bot": false,
      "first_name": "Test",
      "username": "testuser"
    },
    "chat": {
      "id": 987654321,
      "first_name": "Test",
      "username": "testuser",
      "type": "private"
    },
    "date": 1625097601,
    "text": "my screen is not working"
  }
}
2025-07-06T18:46:50: ✅ Created support issue for user 987654321: Display Problem
2025-07-06T18:46:50: 🎟️ Created support issue: Display Problem - Display Issues
2025-07-06T18:46:51: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "my screen is not working" {"service":"weaviate-service","timestamp":"2025-07-06T17:46:51.599Z"}
2025-07-06T18:46:51: 💾 Cached search results for: "my screen is not working"
2025-07-06T18:47:32: 📨 Webhook received: 2025-07-06T17:47:32.847Z
2025-07-06T18:47:32: 📨 Request body: {
  "update_id": 706612688,
  "message": {
    "message_id": 507,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824052,
    "text": "My screen is now working"
  }
}
2025-07-06T18:47:34: ✅ Created support issue for user 5560551341: Display Problem
2025-07-06T18:47:34: 🎟️ Created support issue: Display Problem - Display Issues
2025-07-06T18:47:35: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "My screen is now working" {"service":"weaviate-service","timestamp":"2025-07-06T17:47:35.006Z"}
2025-07-06T18:47:35: 💾 Cached search results for: "My screen is now working"
2025-07-06T18:47:46: 📨 Webhook received: 2025-07-06T17:47:46.062Z
2025-07-06T18:47:46: 📨 Request body: {
  "update_id": 706612689,
  "message": {
    "message_id": 510,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824066,
    "text": "My screen isn't working"
  }
}
2025-07-06T18:47:47: ✅ Created support issue for user 5560551341: Display Problem
2025-07-06T18:47:47: 🎟️ Created support issue: Display Problem - Display Issues
2025-07-06T18:47:48: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "My screen isn't working" {"service":"weaviate-service","timestamp":"2025-07-06T17:47:48.218Z"}
2025-07-06T18:47:48: 💾 Cached search results for: "My screen isn't working"
2025-07-06T18:48:11: 📨 Webhook received: 2025-07-06T17:48:11.149Z
2025-07-06T18:48:11: 📨 Request body: {
  "update_id": 706612690,
  "message": {
    "message_id": 513,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824091,
    "text": "The screen is locked"
  }
}
2025-07-06T18:48:12: ✅ Created support issue for user 5560551341: Display Problem
2025-07-06T18:48:12: 🎟️ Created support issue: Display Problem - Display Issues
2025-07-06T18:48:12: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "The screen is locked" {"service":"weaviate-service","timestamp":"2025-07-06T17:48:12.500Z"}
2025-07-06T18:48:12: 💾 Cached search results for: "The screen is locked"
2025-07-06T19:01:25: 📨 Webhook received: 2025-07-06T18:01:25.683Z
2025-07-06T19:01:25: 📨 Request body: {
  "update_id": 706612691,
  "message": {
    "message_id": 516,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824885,
    "text": "Yes it worked"
  }
}
2025-07-06T19:01:27: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Yes it worked" {"service":"weaviate-service","timestamp":"2025-07-06T18:01:27.306Z"}
2025-07-06T19:01:27: 💾 Cached search results for: "Yes it worked"
2025-07-06T19:01:44: 📨 Webhook received: 2025-07-06T18:01:44.948Z
2025-07-06T19:01:44: 📨 Request body: {
  "update_id": 706612692,
  "message": {
    "message_id": 519,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824904,
    "text": "How do I select a WiFi network"
  }
}
2025-07-06T19:02:07: 📨 Webhook received: 2025-07-06T18:02:07.433Z
2025-07-06T19:02:07: 📨 Request body: {
  "update_id": 706612693,
  "message": {
    "message_id": 520,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824927,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:02:19: 📨 Webhook received: 2025-07-06T18:02:19.268Z
2025-07-06T19:02:19: 📨 Request body: {
  "update_id": 706612694,
  "message": {
    "message_id": 522,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824939,
    "text": "How do I select a WiFi network"
  }
}
2025-07-06T19:02:20: ✅ Created support issue for user 5560551341: Connectivity
2025-07-06T19:02:20: 🎟️ Created support issue: Connectivity - Wi-Fi Connection
2025-07-06T19:02:20: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "How do I select a WiFi network" {"service":"weaviate-service","timestamp":"2025-07-06T18:02:20.570Z"}
2025-07-06T19:02:20: 💾 Cached search results for: "How do I select a WiFi network"
2025-07-06T19:03:04: 📨 Webhook received: 2025-07-06T18:03:04.153Z
2025-07-06T19:03:04: 📨 Request body: {
  "update_id": 706612695,
  "message": {
    "message_id": 525,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751824984,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:03:36: 📨 Webhook received: 2025-07-06T18:03:36.370Z
2025-07-06T19:03:36: 📨 Request body: {
  "update_id": 706612696,
  "message": {
    "message_id": 528,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751825016,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:03:38: 📨 Webhook received: 2025-07-06T18:03:38.628Z
2025-07-06T19:03:38: 📨 Request body: {
  "update_id": 706612697,
  "message": {
    "message_id": 530,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751825018,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:04:42: 📨 Webhook received: 2025-07-06T18:04:42.505Z
2025-07-06T19:04:42: 📨 Request body: {
  "update_id": 706612698,
  "message": {
    "message_id": 532,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751825082,
    "text": "What are wake on lan options"
  }
}
2025-07-06T19:04:44: ✅ Created support issue for user 5560551341: General Support
2025-07-06T19:04:44: 🎟️ Created support issue: General Support - General Question
2025-07-06T19:04:44: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "What are wake on lan options" {"service":"weaviate-service","timestamp":"2025-07-06T18:04:44.493Z"}
2025-07-06T19:04:44: 💾 Cached search results for: "What are wake on lan options"
2025-07-06T19:05:20: 
Shutting down gracefully...
2025-07-06T19:40:28: 🚀 AI Product Support Agent running on port 3000
2025-07-06T19:40:28: 🌐 Web interface: http://localhost:3000
2025-07-06T19:40:28: 🤖 Telegram bot: Active
2025-07-06T19:40:28: 📊 Database: Connected
2025-07-06T19:40:28: 🧠 OpenAI: Connected
2025-07-06T19:40:28: ✅ Redis connected
2025-07-06T19:40:28: ✅ Redis connected
2025-07-06T19:40:28: 🔥 Warming up cache...
2025-07-06T19:40:28: 🔥 Warming up cache...
2025-07-06T19:40:28: ✅ Cache warmed up successfully
2025-07-06T19:40:28: ✅ Cache warmed up successfully
2025-07-06T19:40:28: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T18:40:28.392Z"}
2025-07-06T19:40:28: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T18:40:28.402Z"}
2025-07-06T19:40:28: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T18:40:28.428Z"}
2025-07-06T19:40:28: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T18:40:28.430Z"}
2025-07-06T19:40:28: ✅ Telegram bot: Hybrid search initialized
2025-07-06T19:40:28: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T18:40:28.434Z"}
2025-07-06T19:40:28: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T18:40:28.437Z"}
2025-07-06T19:40:28: Database connection successful
2025-07-06T19:40:28: Webhook set successfully
2025-07-06T19:40:28: Telegram bot webhook set up
2025-07-06T19:40:28: All services initialized successfully
2025-07-06T19:40:44: 📨 Webhook received: 2025-07-06T18:40:44.508Z
2025-07-06T19:40:44: 📨 Request body: {
  "update_id": 706612747,
  "message": {
    "message_id": 640,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827182,
    "text": "/issues",
    "entities": [
      {
        "offset": 0,
        "length": 7,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:40:44: 📨 Webhook received: 2025-07-06T18:40:44.767Z
2025-07-06T19:40:44: 📨 Request body: {
  "update_id": 706612748,
  "message": {
    "message_id": 641,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827201,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:40:53: 📨 Webhook received: 2025-07-06T18:40:53.227Z
2025-07-06T19:40:53: 📨 Request body: {
  "update_id": 706612749,
  "message": {
    "message_id": 644,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827253,
    "text": "/mydevice",
    "entities": [
      {
        "offset": 0,
        "length": 9,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:40:58: 📨 Webhook received: 2025-07-06T18:40:58.809Z
2025-07-06T19:40:58: 📨 Request body: {
  "update_id": 706612750,
  "message": {
    "message_id": 646,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827258,
    "text": "/devices",
    "entities": [
      {
        "offset": 0,
        "length": 8,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:41:02: 📨 Webhook received: 2025-07-06T18:41:02.796Z
2025-07-06T19:41:02: 📨 Request body: {
  "update_id": 706612751,
  "message": {
    "message_id": 648,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827262,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:41:12: 📨 Webhook received: 2025-07-06T18:41:12.525Z
2025-07-06T19:41:12: 📨 Request body: {
  "update_id": 706612752,
  "message": {
    "message_id": 650,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827272,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:41:18: 📨 Webhook received: 2025-07-06T18:41:18.308Z
2025-07-06T19:41:18: 📨 Request body: {
  "update_id": 706612753,
  "message": {
    "message_id": 652,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827278,
    "text": "/mydevice",
    "entities": [
      {
        "offset": 0,
        "length": 9,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:41:23: 📨 Webhook received: 2025-07-06T18:41:23.156Z
2025-07-06T19:41:23: 📨 Request body: {
  "update_id": 706612754,
  "message": {
    "message_id": 654,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827283,
    "text": "Yes"
  }
}
2025-07-06T19:41:24: ⚡ Cache hit for: "Yes"
2025-07-06T19:41:24: [32minfo[39m: ⚡ Cache hit for search: "Yes" {"service":"weaviate-service","timestamp":"2025-07-06T18:41:24.503Z"}
2025-07-06T19:41:33: 📨 Webhook received: 2025-07-06T18:41:33.721Z
2025-07-06T19:41:33: 📨 Request body: {
  "update_id": 706612755,
  "message": {
    "message_id": 657,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827293,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:41:44: 📨 Webhook received: 2025-07-06T18:41:44.277Z
2025-07-06T19:41:44: 📨 Request body: {
  "update_id": 706612756,
  "message": {
    "message_id": 659,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827304,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:41:56: 📨 Webhook received: 2025-07-06T18:41:56.254Z
2025-07-06T19:41:56: 📨 Request body: {
  "update_id": 706612757,
  "message": {
    "message_id": 661,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827316,
    "text": "/issues",
    "entities": [
      {
        "offset": 0,
        "length": 7,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:42:03: 📨 Webhook received: 2025-07-06T18:42:03.054Z
2025-07-06T19:42:03: 📨 Request body: {
  "update_id": 706612758,
  "message": {
    "message_id": 663,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827323,
    "text": "/devices",
    "entities": [
      {
        "offset": 0,
        "length": 8,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:42:11: 📨 Webhook received: 2025-07-06T18:42:11.969Z
2025-07-06T19:42:11: 📨 Request body: {
  "update_id": 706612759,
  "message": {
    "message_id": 665,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827331,
    "text": "/mydevice",
    "entities": [
      {
        "offset": 0,
        "length": 9,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:42:42: 
Shutting down gracefully...
2025-07-06T19:42:44: 🚀 AI Product Support Agent running on port 3000
2025-07-06T19:42:44: 🌐 Web interface: http://localhost:3000
2025-07-06T19:42:44: 🤖 Telegram bot: Active
2025-07-06T19:42:44: 📊 Database: Connected
2025-07-06T19:42:44: 🧠 OpenAI: Connected
2025-07-06T19:42:44: ✅ Redis connected
2025-07-06T19:42:44: ✅ Redis connected
2025-07-06T19:42:44: 🔥 Warming up cache...
2025-07-06T19:42:44: 🔥 Warming up cache...
2025-07-06T19:42:44: ✅ Cache warmed up successfully
2025-07-06T19:42:44: ✅ Cache warmed up successfully
2025-07-06T19:42:44: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T18:42:44.092Z"}
2025-07-06T19:42:44: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T18:42:44.102Z"}
2025-07-06T19:42:44: Database connection successful
2025-07-06T19:42:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T18:42:44.195Z"}
2025-07-06T19:42:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T18:42:44.197Z"}
2025-07-06T19:42:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T18:42:44.203Z"}
2025-07-06T19:42:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T18:42:44.204Z"}
2025-07-06T19:42:44: ✅ Telegram bot: Hybrid search initialized
2025-07-06T19:42:44: Webhook set successfully
2025-07-06T19:42:44: Telegram bot webhook set up
2025-07-06T19:42:44: All services initialized successfully
2025-07-06T19:43:48: 📨 Webhook received: 2025-07-06T18:43:48.876Z
2025-07-06T19:43:48: 📨 Request body: {
  "update_id": 706612760,
  "message": {
    "message_id": 667,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827428,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:43:52: 📨 Webhook received: 2025-07-06T18:43:52.540Z
2025-07-06T19:43:52: 📨 Request body: {
  "update_id": 706612761,
  "message": {
    "message_id": 669,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827432,
    "text": "/issues",
    "entities": [
      {
        "offset": 0,
        "length": 7,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:43:57: 📨 Webhook received: 2025-07-06T18:43:57.424Z
2025-07-06T19:43:57: 📨 Request body: {
  "update_id": 706612762,
  "message": {
    "message_id": 671,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827437,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:43:59: 📨 Webhook received: 2025-07-06T18:43:59.083Z
2025-07-06T19:43:59: 📨 Request body: {
  "update_id": 706612763,
  "message": {
    "message_id": 673,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827439,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:04: 📨 Webhook received: 2025-07-06T18:44:04.726Z
2025-07-06T19:44:04: 📨 Request body: {
  "update_id": 706612764,
  "message": {
    "message_id": 675,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827444,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:07: 📨 Webhook received: 2025-07-06T18:44:07.647Z
2025-07-06T19:44:07: 📨 Request body: {
  "update_id": 706612765,
  "message": {
    "message_id": 677,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827447,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:24: 📨 Webhook received: 2025-07-06T18:44:24.184Z
2025-07-06T19:44:24: 📨 Request body: {
  "update_id": 706612766,
  "message": {
    "message_id": 679,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827464,
    "text": "/support",
    "entities": [
      {
        "offset": 0,
        "length": 8,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:31: 📨 Webhook received: 2025-07-06T18:44:31.928Z
2025-07-06T19:44:31: 📨 Request body: {
  "update_id": 706612767,
  "message": {
    "message_id": 681,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827471,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:35: 📨 Webhook received: 2025-07-06T18:44:35.448Z
2025-07-06T19:44:35: 📨 Request body: {
  "update_id": 706612768,
  "message": {
    "message_id": 683,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827475,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:39: 📨 Webhook received: 2025-07-06T18:44:39.893Z
2025-07-06T19:44:39: 📨 Request body: {
  "update_id": 706612769,
  "message": {
    "message_id": 685,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827479,
    "text": "/devices",
    "entities": [
      {
        "offset": 0,
        "length": 8,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:42: 📨 Webhook received: 2025-07-06T18:44:42.725Z
2025-07-06T19:44:42: 📨 Request body: {
  "update_id": 706612770,
  "message": {
    "message_id": 687,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827482,
    "text": "/mydevice",
    "entities": [
      {
        "offset": 0,
        "length": 9,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:44:50: 📨 Webhook received: 2025-07-06T18:44:50.271Z
2025-07-06T19:44:50: 📨 Request body: {
  "update_id": 706612771,
  "message": {
    "message_id": 689,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827490,
    "text": "Yes"
  }
}
2025-07-06T19:44:52: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Yes" {"service":"weaviate-service","timestamp":"2025-07-06T18:44:52.286Z"}
2025-07-06T19:44:52: 💾 Cached search results for: "Yes"
2025-07-06T19:45:02: 📨 Webhook received: 2025-07-06T18:45:02.651Z
2025-07-06T19:45:02: 📨 Request body: {
  "update_id": 706612772,
  "message": {
    "message_id": 692,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827502,
    "text": "Sure what's do you need?"
  }
}
2025-07-06T19:45:04: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Sure what's do you need?" {"service":"weaviate-service","timestamp":"2025-07-06T18:45:04.299Z"}
2025-07-06T19:45:04: 💾 Cached search results for: "Sure what's do you need?"
2025-07-06T19:46:53: 📨 Webhook received: 2025-07-06T18:46:53.833Z
2025-07-06T19:46:53: 📨 Request body: {
  "test": "data"
}
2025-07-06T19:47:24: 📨 Webhook received: 2025-07-06T18:47:24.007Z
2025-07-06T19:47:24: 📨 Request body: {
  "test": "data"
}
2025-07-06T19:48:05: 📨 Webhook received: 2025-07-06T18:48:05.176Z
2025-07-06T19:48:05: 📨 Request body: {
  "test": "data"
}
2025-07-06T19:49:27: 📨 Webhook received: 2025-07-06T18:49:27.828Z
2025-07-06T19:49:27: 📨 Request body: {
  "update_id": 706612773,
  "message": {
    "message_id": 695,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827767,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:49:30: 📨 Webhook received: 2025-07-06T18:49:30.468Z
2025-07-06T19:49:30: 📨 Request body: {
  "update_id": 706612774,
  "message": {
    "message_id": 697,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827770,
    "text": "/mydevice",
    "entities": [
      {
        "offset": 0,
        "length": 9,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:49:35: 📨 Webhook received: 2025-07-06T18:49:35.069Z
2025-07-06T19:49:35: 📨 Request body: {
  "update_id": 706612775,
  "message": {
    "message_id": 699,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827775,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:49:38: 📨 Webhook received: 2025-07-06T18:49:38.729Z
2025-07-06T19:49:38: 📨 Request body: {
  "update_id": 706612776,
  "message": {
    "message_id": 701,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827778,
    "text": "/devices",
    "entities": [
      {
        "offset": 0,
        "length": 8,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:49:46: 📨 Webhook received: 2025-07-06T18:49:46.332Z
2025-07-06T19:49:46: 📨 Request body: {
  "update_id": 706612777,
  "message": {
    "message_id": 703,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827786,
    "text": "WM65B"
  }
}
2025-07-06T19:49:46: 🔍 Detected product: WM65B (Samsung)
2025-07-06T19:49:46: ✅ Created user device tracking for 5560551341: WM65B
2025-07-06T19:49:48: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "WM65B" {"service":"weaviate-service","timestamp":"2025-07-06T18:49:48.261Z"}
2025-07-06T19:50:12: 📨 Webhook received: 2025-07-06T18:50:12.916Z
2025-07-06T19:50:12: 📨 Request body: {
  "update_id": 706612778,
  "message": {
    "message_id": 706,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827812,
    "text": "How many HDMI ports does it have?"
  }
}
2025-07-06T19:50:14: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "How many HDMI ports does it have?" {"service":"weaviate-service","timestamp":"2025-07-06T18:50:14.331Z"}
2025-07-06T19:50:14: 💾 Cached search results for: "How many HDMI ports does it have?"
2025-07-06T19:51:49: 📨 Webhook received: 2025-07-06T18:51:49.241Z
2025-07-06T19:51:49: 📨 Request body: {
  "update_id": 706612779,
  "message": {
    "message_id": 709,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827909,
    "text": "No it only has 1"
  }
}
2025-07-06T19:51:50: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "No it only has 1" {"service":"weaviate-service","timestamp":"2025-07-06T18:51:50.911Z"}
2025-07-06T19:51:50: 💾 Cached search results for: "No it only has 1"
2025-07-06T19:52:10: 📨 Webhook received: 2025-07-06T18:52:10.579Z
2025-07-06T19:52:10: 📨 Request body: {
  "update_id": 706612780,
  "message": {
    "message_id": 712,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827930,
    "text": "How many HDMI ports does the wm85b have?"
  }
}
2025-07-06T19:52:10: 🔍 Detected product: WM85B (Samsung)
2025-07-06T19:52:10: ✅ Created user device tracking for 5560551341: WM85B
2025-07-06T19:52:11: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "How many HDMI ports does the wm85b have?" {"service":"weaviate-service","timestamp":"2025-07-06T18:52:11.876Z"}
2025-07-06T19:52:55: 📨 Webhook received: 2025-07-06T18:52:55.353Z
2025-07-06T19:52:55: 📨 Request body: {
  "update_id": 706612781,
  "message": {
    "message_id": 715,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827975,
    "text": "Which models have screen recording?"
  }
}
2025-07-06T19:52:56: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have screen recording?" {"service":"weaviate-service","timestamp":"2025-07-06T18:52:56.640Z"}
2025-07-06T19:52:56: 💾 Cached search results for: "Which models have screen recording?"
2025-07-06T19:53:17: 📨 Webhook received: 2025-07-06T18:53:17.575Z
2025-07-06T19:53:17: 📨 Request body: {
  "update_id": 706612782,
  "message": {
    "message_id": 718,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751827997,
    "text": "Screen_recording"
  }
}
2025-07-06T19:53:18: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Screen_recording" {"service":"weaviate-service","timestamp":"2025-07-06T18:53:18.804Z"}
2025-07-06T19:53:18: 💾 Cached search results for: "Screen_recording"
2025-07-06T19:53:40: 
Shutting down gracefully...
2025-07-06T19:53:44: 🚀 AI Product Support Agent running on port 3000
2025-07-06T19:53:44: 🌐 Web interface: http://localhost:3000
2025-07-06T19:53:44: 🤖 Telegram bot: Active
2025-07-06T19:53:44: 📊 Database: Connected
2025-07-06T19:53:44: 🧠 OpenAI: Connected
2025-07-06T19:53:44: ✅ Redis connected
2025-07-06T19:53:44: ✅ Redis connected
2025-07-06T19:53:44: 🔥 Warming up cache...
2025-07-06T19:53:44: 🔥 Warming up cache...
2025-07-06T19:53:44: ✅ Cache warmed up successfully
2025-07-06T19:53:44: ✅ Cache warmed up successfully
2025-07-06T19:53:44: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T18:53:44.040Z"}
2025-07-06T19:53:44: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T18:53:44.048Z"}
2025-07-06T19:53:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T18:53:44.127Z"}
2025-07-06T19:53:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T18:53:44.128Z"}
2025-07-06T19:53:44: ✅ Telegram bot: Hybrid search initialized
2025-07-06T19:53:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T18:53:44.134Z"}
2025-07-06T19:53:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T18:53:44.136Z"}
2025-07-06T19:53:44: Database connection successful
2025-07-06T19:53:44: Webhook set successfully
2025-07-06T19:53:44: Telegram bot webhook set up
2025-07-06T19:53:44: All services initialized successfully
2025-07-06T19:54:00: 📨 Webhook received: 2025-07-06T18:54:00.587Z
2025-07-06T19:54:00: 📨 Request body: {
  "update_id": 706612783,
  "message": {
    "message_id": 721,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828040,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:54:11: 📨 Webhook received: 2025-07-06T18:54:11.762Z
2025-07-06T19:54:11: 📨 Request body: {
  "update_id": 706612784,
  "message": {
    "message_id": 723,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828051,
    "text": "Why wa65f is screen locked"
  }
}
2025-07-06T19:54:11: 🔍 Detected product: WA65F (Samsung)
2025-07-06T19:54:11: ✅ Updated user device tracking for 5560551341: WA65F
2025-07-06T19:54:12: ✅ Created support issue for user 5560551341: Display Problem
2025-07-06T19:54:12: 🎟️ Created support issue: Display Problem - Display Issues
2025-07-06T19:54:13: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Why wa65f is screen locked" {"service":"weaviate-service","timestamp":"2025-07-06T18:54:13.226Z"}
2025-07-06T19:54:38: 📨 Webhook received: 2025-07-06T18:54:38.339Z
2025-07-06T19:54:38: 📨 Request body: {
  "update_id": 706612785,
  "message": {
    "message_id": 726,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828078,
    "text": "What about using the remote to unlock"
  }
}
2025-07-06T19:54:39: ✅ Created support issue for user 5560551341: General Support
2025-07-06T19:54:39: 🎟️ Created support issue: General Support - General Question
2025-07-06T19:54:39: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "What about using the remote to unlock" {"service":"weaviate-service","timestamp":"2025-07-06T18:54:39.961Z"}
2025-07-06T19:54:39: 💾 Cached search results for: "What about using the remote to unlock"
2025-07-06T19:55:04: 📨 Webhook received: 2025-07-06T18:55:04.812Z
2025-07-06T19:55:04: 📨 Request body: {
  "update_id": 706612786,
  "message": {
    "message_id": 729,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828104,
    "text": "What if the screen freeze isn't working?"
  }
}
2025-07-06T19:55:05: ✅ Created support issue for user 5560551341: Display Problem
2025-07-06T19:55:05: 🎟️ Created support issue: Display Problem - Display Issues
2025-07-06T19:55:06: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "What if the screen freeze isn't working?" {"service":"weaviate-service","timestamp":"2025-07-06T18:55:06.147Z"}
2025-07-06T19:55:06: 💾 Cached search results for: "What if the screen freeze isn't working?"
2025-07-06T19:57:01: 📨 Webhook received: 2025-07-06T18:57:01.624Z
2025-07-06T19:57:01: 📨 Request body: {
  "update_id": 706612787,
  "message": {
    "message_id": 732,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828221,
    "text": "Great that worked"
  }
}
2025-07-06T19:57:03: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Great that worked" {"service":"weaviate-service","timestamp":"2025-07-06T18:57:03.799Z"}
2025-07-06T19:57:03: 💾 Cached search results for: "Great that worked"
2025-07-06T19:57:24: 📨 Webhook received: 2025-07-06T18:57:24.409Z
2025-07-06T19:57:24: 📨 Request body: {
  "update_id": 706612788,
  "message": {
    "message_id": 735,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828244,
    "text": "You are helpful!"
  }
}
2025-07-06T19:57:25: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "You are helpful!" {"service":"weaviate-service","timestamp":"2025-07-06T18:57:25.902Z"}
2025-07-06T19:57:25: 💾 Cached search results for: "You are helpful!"
2025-07-06T19:59:09: 📨 Webhook received: 2025-07-06T18:59:09.009Z
2025-07-06T19:59:09: 📨 Request body: {
  "update_id": 706612789,
  "message": {
    "message_id": 742,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828348,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:59:10: 📨 Webhook received: 2025-07-06T18:59:10.674Z
2025-07-06T19:59:10: 📨 Request body: {
  "update_id": 706612790,
  "message": {
    "message_id": 744,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828350,
    "text": "/categories",
    "entities": [
      {
        "offset": 0,
        "length": 11,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:59:18: 📨 Webhook received: 2025-07-06T18:59:18.971Z
2025-07-06T19:59:18: 📨 Request body: {
  "update_id": 706612791,
  "message": {
    "message_id": 746,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828358,
    "text": "/reset",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:59:22: 📨 Webhook received: 2025-07-06T18:59:21.999Z
2025-07-06T19:59:22: 📨 Request body: {
  "update_id": 706612792,
  "message": {
    "message_id": 748,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828361,
    "text": "/help",
    "entities": [
      {
        "offset": 0,
        "length": 5,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:59:24: 📨 Webhook received: 2025-07-06T18:59:24.572Z
2025-07-06T19:59:24: 📨 Request body: {
  "update_id": 706612793,
  "message": {
    "message_id": 750,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828364,
    "text": "/mydevice",
    "entities": [
      {
        "offset": 0,
        "length": 9,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:59:27: 📨 Webhook received: 2025-07-06T18:59:27.289Z
2025-07-06T19:59:27: 📨 Request body: {
  "update_id": 706612794,
  "message": {
    "message_id": 752,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828367,
    "text": "/devices",
    "entities": [
      {
        "offset": 0,
        "length": 8,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T19:59:35: 📨 Webhook received: 2025-07-06T18:59:35.265Z
2025-07-06T19:59:35: 📨 Request body: {
  "update_id": 706612795,
  "message": {
    "message_id": 754,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828375,
    "text": "/issues",
    "entities": [
      {
        "offset": 0,
        "length": 7,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T20:00:56: 📨 Webhook received: 2025-07-06T19:00:56.677Z
2025-07-06T20:00:56: 📨 Request body: {
  "update_id": 706612796,
  "message": {
    "message_id": 756,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828456,
    "text": "How many HDMI inputs does the wa86c have?"
  }
}
2025-07-06T20:00:56: 🔍 Detected product: WA86C (Samsung)
2025-07-06T20:00:56: ✅ Created user device tracking for 5560551341: WA86C
2025-07-06T20:00:58: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "How many HDMI inputs does the wa86c have?" {"service":"weaviate-service","timestamp":"2025-07-06T19:00:58.614Z"}
2025-07-06T20:01:16: 📨 Webhook received: 2025-07-06T19:01:16.002Z
2025-07-06T20:01:16: 📨 Request body: {
  "update_id": 706612797,
  "message": {
    "message_id": 759,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828475,
    "text": "How many VGA inputs does this model have?"
  }
}
2025-07-06T20:01:17: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "How many VGA inputs does this model have?" {"service":"weaviate-service","timestamp":"2025-07-06T19:01:17.463Z"}
2025-07-06T20:01:17: 💾 Cached search results for: "How many VGA inputs does this model have?"
2025-07-06T20:01:41: 📨 Webhook received: 2025-07-06T19:01:41.437Z
2025-07-06T20:01:41: 📨 Request body: {
  "update_id": 706612798,
  "message": {
    "message_id": 762,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828501,
    "text": "Oh ok so does it have an OPS slot?"
  }
}
2025-07-06T20:01:42: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Oh ok so does it have an OPS slot?" {"service":"weaviate-service","timestamp":"2025-07-06T19:01:42.764Z"}
2025-07-06T20:01:42: 💾 Cached search results for: "Oh ok so does it have an OPS slot?"
2025-07-06T20:05:48: 📨 Webhook received: 2025-07-06T19:05:48.398Z
2025-07-06T20:05:48: 📨 Request body: {
  "update_id": 706612799,
  "message": {
    "message_id": 765,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828748,
    "text": "How many VGA ports does the wa65c have?"
  }
}
2025-07-06T20:05:48: 🔍 Detected product: WA65C (Samsung)
2025-07-06T20:05:48: ✅ Updated user device tracking for 5560551341: WA65C
2025-07-06T20:05:50: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "How many VGA ports does the wa65c have?" {"service":"weaviate-service","timestamp":"2025-07-06T19:05:50.762Z"}
2025-07-06T20:07:56: 
Shutting down gracefully...
2025-07-06T20:07:59: 🚀 AI Product Support Agent running on port 3000
2025-07-06T20:07:59: 🌐 Web interface: http://localhost:3000
2025-07-06T20:07:59: 🤖 Telegram bot: Active
2025-07-06T20:07:59: 📊 Database: Connected
2025-07-06T20:07:59: 🧠 OpenAI: Connected
2025-07-06T20:07:59: ✅ Redis connected
2025-07-06T20:07:59: ✅ Redis connected
2025-07-06T20:07:59: 🔥 Warming up cache...
2025-07-06T20:07:59: 🔥 Warming up cache...
2025-07-06T20:07:59: Database connection successful
2025-07-06T20:07:59: ✅ Cache warmed up successfully
2025-07-06T20:07:59: ✅ Cache warmed up successfully
2025-07-06T20:07:59: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:07:59.516Z"}
2025-07-06T20:07:59: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:07:59.528Z"}
2025-07-06T20:07:59: Webhook set successfully
2025-07-06T20:07:59: Telegram bot webhook set up
2025-07-06T20:07:59: All services initialized successfully
2025-07-06T20:07:59: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:07:59.632Z"}
2025-07-06T20:07:59: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:07:59.633Z"}
2025-07-06T20:07:59: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:07:59.640Z"}
2025-07-06T20:07:59: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:07:59.641Z"}
2025-07-06T20:07:59: ✅ Telegram bot: Hybrid search initialized
2025-07-06T20:09:33: 📨 Webhook received: 2025-07-06T19:09:33.450Z
2025-07-06T20:09:33: 📨 Request body: {
  "update_id": 706612800,
  "message": {
    "message_id": 768,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751828973,
    "text": "How many VGA ports does the wa65c have?"
  }
}
2025-07-06T20:09:33: 🔍 Detected product: WA65C (Samsung)
2025-07-06T20:09:33: ✅ Updated user device tracking for 5560551341: WA65C
2025-07-06T20:09:35: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "How many VGA ports does the wa65c have?" {"service":"weaviate-service","timestamp":"2025-07-06T19:09:35.649Z"}
2025-07-06T20:12:18: 
Shutting down gracefully...
2025-07-06T20:12:21: 🚀 AI Product Support Agent running on port 3000
2025-07-06T20:12:21: 🌐 Web interface: http://localhost:3000
2025-07-06T20:12:21: 🤖 Telegram bot: Active
2025-07-06T20:12:21: 📊 Database: Connected
2025-07-06T20:12:21: 🧠 OpenAI: Connected
2025-07-06T20:12:21: ✅ Redis connected
2025-07-06T20:12:21: ✅ Redis connected
2025-07-06T20:12:21: 🔥 Warming up cache...
2025-07-06T20:12:21: 🔥 Warming up cache...
2025-07-06T20:12:21: Database connection successful
2025-07-06T20:12:22: ✅ Cache warmed up successfully
2025-07-06T20:12:22: ✅ Cache warmed up successfully
2025-07-06T20:12:22: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:12:22.034Z"}
2025-07-06T20:12:22: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:12:22.050Z"}
2025-07-06T20:12:22: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:12:22.097Z"}
2025-07-06T20:12:22: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:12:22.099Z"}
2025-07-06T20:12:22: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:12:22.110Z"}
2025-07-06T20:12:22: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:12:22.112Z"}
2025-07-06T20:12:22: ✅ Telegram bot: Hybrid search initialized
2025-07-06T20:12:22: Webhook set successfully
2025-07-06T20:12:22: Telegram bot webhook set up
2025-07-06T20:12:22: All services initialized successfully
2025-07-06T20:12:23: 📨 Webhook received: 2025-07-06T19:12:23.855Z
2025-07-06T20:12:23: 📨 Request body: {
  "update_id": 706612801,
  "message": {
    "message_id": 771,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829141,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:12:30: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:12:30.678Z"}
2025-07-06T20:12:30: 💾 Cached search results for: "Which models have OPS?"
2025-07-06T20:12:55: 📨 Webhook received: 2025-07-06T19:12:55.552Z
2025-07-06T20:12:55: 📨 Request body: {
  "update_id": 706612802,
  "message": {
    "message_id": 774,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829175,
    "text": "Check again"
  }
}
2025-07-06T20:12:57: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Check again" {"service":"weaviate-service","timestamp":"2025-07-06T19:12:57.117Z"}
2025-07-06T20:12:57: 💾 Cached search results for: "Check again"
2025-07-06T20:13:26: 📨 Webhook received: 2025-07-06T19:13:26.694Z
2025-07-06T20:13:26: 📨 Request body: {
  "update_id": 706612803,
  "message": {
    "message_id": 777,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829206,
    "text": "/reset",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T20:13:37: 📨 Webhook received: 2025-07-06T19:13:37.068Z
2025-07-06T20:13:37: 📨 Request body: {
  "update_id": 706612804,
  "message": {
    "message_id": 779,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829217,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:13:38: ⚡ Cache hit for: "Which models have OPS?"
2025-07-06T20:13:38: [32minfo[39m: ⚡ Cache hit for search: "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:13:38.486Z"}
2025-07-06T20:13:54: 📨 Webhook received: 2025-07-06T19:13:54.467Z
2025-07-06T20:13:54: 📨 Request body: {
  "update_id": 706612805,
  "message": {
    "message_id": 782,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829234,
    "text": "Check again"
  }
}
2025-07-06T20:13:55: ⚡ Cache hit for: "Check again"
2025-07-06T20:13:55: [32minfo[39m: ⚡ Cache hit for search: "Check again" {"service":"weaviate-service","timestamp":"2025-07-06T19:13:55.323Z"}
2025-07-06T20:14:09: 📨 Webhook received: 2025-07-06T19:14:09.891Z
2025-07-06T20:14:09: 📨 Request body: {
  "update_id": 706612806,
  "message": {
    "message_id": 785,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829249,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:14:11: ⚡ Cache hit for: "Which models have OPS?"
2025-07-06T20:14:11: [32minfo[39m: ⚡ Cache hit for search: "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:14:11.562Z"}
2025-07-06T20:14:38: 
Shutting down gracefully...
2025-07-06T20:14:40: 🚀 AI Product Support Agent running on port 3000
2025-07-06T20:14:40: 🌐 Web interface: http://localhost:3000
2025-07-06T20:14:40: 🤖 Telegram bot: Active
2025-07-06T20:14:40: 📊 Database: Connected
2025-07-06T20:14:40: 🧠 OpenAI: Connected
2025-07-06T20:14:41: ✅ Redis connected
2025-07-06T20:14:41: ✅ Redis connected
2025-07-06T20:14:41: 🔥 Warming up cache...
2025-07-06T20:14:41: 🔥 Warming up cache...
2025-07-06T20:14:41: ✅ Cache warmed up successfully
2025-07-06T20:14:41: ✅ Cache warmed up successfully
2025-07-06T20:14:41: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:14:41.020Z"}
2025-07-06T20:14:41: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:14:41.028Z"}
2025-07-06T20:14:41: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:14:41.153Z"}
2025-07-06T20:14:41: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:14:41.155Z"}
2025-07-06T20:14:41: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:14:41.159Z"}
2025-07-06T20:14:41: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:14:41.160Z"}
2025-07-06T20:14:41: ✅ Telegram bot: Hybrid search initialized
2025-07-06T20:14:41: Database connection successful
2025-07-06T20:14:41: Webhook set successfully
2025-07-06T20:14:41: Telegram bot webhook set up
2025-07-06T20:14:41: All services initialized successfully
2025-07-06T20:15:14: 📨 Webhook received: 2025-07-06T19:15:14.281Z
2025-07-06T20:15:14: 📨 Request body: {
  "update_id": 706612807,
  "message": {
    "message_id": 788,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829314,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:15:15: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:15:15.681Z"}
2025-07-06T20:15:15: 💾 Cached search results for: "Which models have OPS?"
2025-07-06T20:15:30: 📨 Webhook received: 2025-07-06T19:15:30.456Z
2025-07-06T20:15:30: 📨 Request body: {
  "update_id": 706612808,
  "message": {
    "message_id": 791,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829330,
    "text": "/reset",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T20:15:35: 📨 Webhook received: 2025-07-06T19:15:35.947Z
2025-07-06T20:15:35: 📨 Request body: {
  "update_id": 706612809,
  "message": {
    "message_id": 793,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829335,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T20:15:43: 📨 Webhook received: 2025-07-06T19:15:43.769Z
2025-07-06T20:15:43: 📨 Request body: {
  "update_id": 706612810,
  "message": {
    "message_id": 795,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829343,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:15:44: ⚡ Cache hit for: "Which models have OPS?"
2025-07-06T20:15:44: [32minfo[39m: ⚡ Cache hit for search: "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:15:44.746Z"}
2025-07-06T20:16:37: 📨 Webhook received: 2025-07-06T19:16:37.102Z
2025-07-06T20:16:37: 📨 Request body: {
  "update_id": 706612811,
  "message": {
    "message_id": 798,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829397,
    "text": "Which models use Tizen?"
  }
}
2025-07-06T20:16:38: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Which models use Tizen?" {"service":"weaviate-service","timestamp":"2025-07-06T19:16:38.686Z"}
2025-07-06T20:18:59: 📨 Webhook received: 2025-07-06T19:18:59.558Z
2025-07-06T20:18:59: 📨 Request body: {
  "update_id": 706612812,
  "message": {
    "message_id": 801,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829539,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:19:00: ⚡ Cache hit for: "Which models have OPS?"
2025-07-06T20:19:00: [32minfo[39m: ⚡ Cache hit for search: "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:19:00.546Z"}
2025-07-06T20:20:06: 
Shutting down gracefully...
2025-07-06T20:20:13: 🚀 AI Product Support Agent running on port 3000
2025-07-06T20:20:13: 🌐 Web interface: http://localhost:3000
2025-07-06T20:20:13: 🤖 Telegram bot: Active
2025-07-06T20:20:13: 📊 Database: Connected
2025-07-06T20:20:13: 🧠 OpenAI: Connected
2025-07-06T20:20:13: ✅ Redis connected
2025-07-06T20:20:13: ✅ Redis connected
2025-07-06T20:20:13: 🔥 Warming up cache...
2025-07-06T20:20:13: 🔥 Warming up cache...
2025-07-06T20:20:13: ✅ Cache warmed up successfully
2025-07-06T20:20:13: ✅ Cache warmed up successfully
2025-07-06T20:20:13: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:20:13.119Z"}
2025-07-06T20:20:13: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:20:13.133Z"}
2025-07-06T20:20:13: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:20:13.166Z"}
2025-07-06T20:20:13: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:20:13.168Z"}
2025-07-06T20:20:13: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:20:13.173Z"}
2025-07-06T20:20:13: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:20:13.174Z"}
2025-07-06T20:20:13: ✅ Telegram bot: Hybrid search initialized
2025-07-06T20:20:13: Database connection successful
2025-07-06T20:20:13: Webhook set successfully
2025-07-06T20:20:13: Telegram bot webhook set up
2025-07-06T20:20:13: All services initialized successfully
2025-07-06T20:21:42: 
Shutting down gracefully...
2025-07-06T20:21:43: 🚀 AI Product Support Agent running on port 3000
2025-07-06T20:21:43: 🌐 Web interface: http://localhost:3000
2025-07-06T20:21:43: 🤖 Telegram bot: Active
2025-07-06T20:21:43: 📊 Database: Connected
2025-07-06T20:21:43: 🧠 OpenAI: Connected
2025-07-06T20:21:43: ✅ Redis connected
2025-07-06T20:21:43: ✅ Redis connected
2025-07-06T20:21:43: 🔥 Warming up cache...
2025-07-06T20:21:43: 🔥 Warming up cache...
2025-07-06T20:21:43: ✅ Cache warmed up successfully
2025-07-06T20:21:43: ✅ Cache warmed up successfully
2025-07-06T20:21:43: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:21:43.906Z"}
2025-07-06T20:21:43: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:21:43.916Z"}
2025-07-06T20:21:43: Database connection successful
2025-07-06T20:21:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:21:44.005Z"}
2025-07-06T20:21:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:21:44.007Z"}
2025-07-06T20:21:44: ✅ Telegram bot: Hybrid search initialized
2025-07-06T20:21:44: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:21:44.013Z"}
2025-07-06T20:21:44: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:21:44.015Z"}
2025-07-06T20:21:44: Webhook set successfully
2025-07-06T20:21:44: Telegram bot webhook set up
2025-07-06T20:21:44: All services initialized successfully
2025-07-06T20:21:57: 📨 Webhook received: 2025-07-06T19:21:57.834Z
2025-07-06T20:21:57: 📨 Request body: {
  "update_id": 706612813,
  "message": {
    "message_id": 804,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829717,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T20:22:05: 📨 Webhook received: 2025-07-06T19:22:05.672Z
2025-07-06T20:22:05: 📨 Request body: {
  "update_id": 706612814,
  "message": {
    "message_id": 806,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829725,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:22:07: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:22:07.807Z"}
2025-07-06T20:22:07: 💾 Cached search results for: "Which models have OPS?"
2025-07-06T20:23:03: 📨 Webhook received: 2025-07-06T19:23:03.866Z
2025-07-06T20:23:03: 📨 Request body: {
  "update_id": 706612815,
  "message": {
    "message_id": 809,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829783,
    "text": "Which models use Tizen?"
  }
}
2025-07-06T20:23:05: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Which models use Tizen?" {"service":"weaviate-service","timestamp":"2025-07-06T19:23:05.867Z"}
2025-07-06T20:25:29: 
Shutting down gracefully...
2025-07-06T20:25:32: 🚀 AI Product Support Agent running on port 3000
2025-07-06T20:25:33: 🌐 Web interface: http://localhost:3000
2025-07-06T20:25:33: 🤖 Telegram bot: Active
2025-07-06T20:25:33: 📊 Database: Connected
2025-07-06T20:25:33: 🧠 OpenAI: Connected
2025-07-06T20:25:33: ✅ Redis connected
2025-07-06T20:25:33: ✅ Redis connected
2025-07-06T20:25:33: 🔥 Warming up cache...
2025-07-06T20:25:33: 🔥 Warming up cache...
2025-07-06T20:25:33: ✅ Cache warmed up successfully
2025-07-06T20:25:33: ✅ Cache warmed up successfully
2025-07-06T20:25:33: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:25:33.056Z"}
2025-07-06T20:25:33: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:25:33.066Z"}
2025-07-06T20:25:33: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:25:33.216Z"}
2025-07-06T20:25:33: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:25:33.218Z"}
2025-07-06T20:25:33: ✅ Telegram bot: Hybrid search initialized
2025-07-06T20:25:33: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:25:33.231Z"}
2025-07-06T20:25:33: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:25:33.232Z"}
2025-07-06T20:25:33: Database connection successful
2025-07-06T20:25:33: Webhook set successfully
2025-07-06T20:25:33: Telegram bot webhook set up
2025-07-06T20:25:33: All services initialized successfully
2025-07-06T20:26:25: 📨 Webhook received: 2025-07-06T19:26:25.472Z
2025-07-06T20:26:25: 📨 Request body: {
  "update_id": 706612816,
  "message": {
    "message_id": 812,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751829985,
    "text": "Which models use Tizen?"
  }
}
2025-07-06T20:26:28: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Which models use Tizen?" {"service":"weaviate-service","timestamp":"2025-07-06T19:26:28.225Z"}
2025-07-06T20:29:01: [32minfo[39m: ✅ Weaviate search: 20 KB + 0 docs + 0 structured for "display Samsung interactive troubleshooting" {"service":"weaviate-service","timestamp":"2025-07-06T19:29:01.508Z"}
2025-07-06T20:29:01: 💾 Cached search results for: "display Samsung interactive troubleshooting"
2025-07-06T20:30:42: 
Shutting down gracefully...
2025-07-06T20:30:45: 🚀 AI Product Support Agent running on port 3000
2025-07-06T20:30:45: 🌐 Web interface: http://localhost:3000
2025-07-06T20:30:45: 🤖 Telegram bot: Active
2025-07-06T20:30:45: 📊 Database: Connected
2025-07-06T20:30:45: 🧠 OpenAI: Connected
2025-07-06T20:30:45: ✅ Redis connected
2025-07-06T20:30:45: ✅ Redis connected
2025-07-06T20:30:45: 🔥 Warming up cache...
2025-07-06T20:30:45: 🔥 Warming up cache...
2025-07-06T20:30:45: ✅ Cache warmed up successfully
2025-07-06T20:30:45: ✅ Cache warmed up successfully
2025-07-06T20:30:45: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:30:45.433Z"}
2025-07-06T20:30:45: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T19:30:45.447Z"}
2025-07-06T20:30:45: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:30:45.537Z"}
2025-07-06T20:30:45: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:30:45.538Z"}
2025-07-06T20:30:45: ✅ Telegram bot: Hybrid search initialized
2025-07-06T20:30:45: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T19:30:45.545Z"}
2025-07-06T20:30:45: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T19:30:45.547Z"}
2025-07-06T20:30:45: Database connection successful
2025-07-06T20:30:45: Webhook set successfully
2025-07-06T20:30:45: Telegram bot webhook set up
2025-07-06T20:30:45: All services initialized successfully
2025-07-06T20:32:57: 📨 Webhook received: 2025-07-06T19:32:57.765Z
2025-07-06T20:32:57: 📨 Request body: {
  "update_id": 706612817,
  "message": {
    "message_id": 815,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830377,
    "text": "Which models have screen sharing?"
  }
}
2025-07-06T20:32:59: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have screen sharing?" {"service":"weaviate-service","timestamp":"2025-07-06T19:32:59.827Z"}
2025-07-06T20:32:59: 💾 Cached search results for: "Which models have screen sharing?"
2025-07-06T20:33:50: 📨 Webhook received: 2025-07-06T19:33:50.233Z
2025-07-06T20:33:50: 📨 Request body: {
  "update_id": 706612818,
  "message": {
    "message_id": 818,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830430,
    "text": "Which models have screen casting?"
  }
}
2025-07-06T20:33:52: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have screen casting?" {"service":"weaviate-service","timestamp":"2025-07-06T19:33:52.140Z"}
2025-07-06T20:33:52: 💾 Cached search results for: "Which models have screen casting?"
2025-07-06T20:34:19: 📨 Webhook received: 2025-07-06T19:34:19.217Z
2025-07-06T20:34:19: 📨 Request body: {
  "update_id": 706612819,
  "message": {
    "message_id": 821,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830459,
    "text": "Which models have Google play store?"
  }
}
2025-07-06T20:34:20: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Which models have Google play store?" {"service":"weaviate-service","timestamp":"2025-07-06T19:34:20.940Z"}
2025-07-06T20:34:55: 📨 Webhook received: 2025-07-06T19:34:55.866Z
2025-07-06T20:34:55: 📨 Request body: {
  "update_id": 706612820,
  "message": {
    "message_id": 824,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830495,
    "text": "Which models can install apps?"
  }
}
2025-07-06T20:34:57: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models can install apps?" {"service":"weaviate-service","timestamp":"2025-07-06T19:34:57.582Z"}
2025-07-06T20:34:57: 💾 Cached search results for: "Which models can install apps?"
2025-07-06T20:35:21: 📨 Webhook received: 2025-07-06T19:35:21.889Z
2025-07-06T20:35:21: 📨 Request body: {
  "update_id": 706612821,
  "message": {
    "message_id": 827,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830521,
    "text": "Doesnt wm65b have screen casting?"
  }
}
2025-07-06T20:35:21: 🔍 Detected product: WM65B (Samsung)
2025-07-06T20:35:21: ✅ Updated user device tracking for 5560551341: WM65B
2025-07-06T20:35:23: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Doesnt wm65b have screen casting?" {"service":"weaviate-service","timestamp":"2025-07-06T19:35:23.159Z"}
2025-07-06T20:36:29: 📨 Webhook received: 2025-07-06T19:36:29.359Z
2025-07-06T20:36:29: 📨 Request body: {
  "update_id": 706612822,
  "message": {
    "message_id": 830,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830589,
    "text": "Which digital flipchart has screen sharing?"
  }
}
2025-07-06T20:36:30: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Which digital flipchart has screen sharing?" {"service":"weaviate-service","timestamp":"2025-07-06T19:36:30.990Z"}
2025-07-06T20:36:56: 📨 Webhook received: 2025-07-06T19:36:56.675Z
2025-07-06T20:36:56: 📨 Request body: {
  "update_id": 706612823,
  "message": {
    "message_id": 833,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830616,
    "text": "Which digital flipchart has screen casting?"
  }
}
2025-07-06T20:36:57: [32minfo[39m: ✅ Weaviate search: 0 KB + 0 docs + 0 structured for "Which digital flipchart has screen casting?" {"service":"weaviate-service","timestamp":"2025-07-06T19:36:57.820Z"}
2025-07-06T20:37:08: 📨 Webhook received: 2025-07-06T19:37:08.625Z
2025-07-06T20:37:08: 📨 Request body: {
  "update_id": 706612824,
  "message": {
    "message_id": 836,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830628,
    "text": "Which models have screen casting?"
  }
}
2025-07-06T20:37:10: ⚡ Cache hit for: "Which models have screen casting?"
2025-07-06T20:37:10: [32minfo[39m: ⚡ Cache hit for search: "Which models have screen casting?" {"service":"weaviate-service","timestamp":"2025-07-06T19:37:10.031Z"}
2025-07-06T20:38:01: 📨 Webhook received: 2025-07-06T19:38:01.383Z
2025-07-06T20:38:01: 📨 Request body: {
  "update_id": 706612825,
  "message": {
    "message_id": 839,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830681,
    "text": "Which models have screen recording?"
  }
}
2025-07-06T20:38:02: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have screen recording?" {"service":"weaviate-service","timestamp":"2025-07-06T19:38:02.497Z"}
2025-07-06T20:38:02: 💾 Cached search results for: "Which models have screen recording?"
2025-07-06T20:40:45: 📨 Webhook received: 2025-07-06T19:40:45.604Z
2025-07-06T20:40:45: 📨 Request body: {
  "update_id": 706612826,
  "message": {
    "message_id": 842,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751830845,
    "text": "Which models have wifi?"
  }
}
2025-07-06T20:40:47: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have wifi?" {"service":"weaviate-service","timestamp":"2025-07-06T19:40:47.351Z"}
2025-07-06T20:40:47: 💾 Cached search results for: "Which models have wifi?"
2025-07-06T20:44:48: 📨 Webhook received: 2025-07-06T19:44:48.766Z
2025-07-06T20:44:48: 📨 Request body: {
  "update_id": 706612827,
  "message": {
    "message_id": 845,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751831088,
    "text": "Which models have OPS?"
  }
}
2025-07-06T20:44:50: ⚡ Cache hit for: "Which models have OPS?"
2025-07-06T20:44:50: [32minfo[39m: ⚡ Cache hit for search: "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:44:50.746Z"}
2025-07-06T20:49:45: 📨 Webhook received: 2025-07-06T19:49:45.727Z
2025-07-06T20:49:45: 📨 Request body: {
  "update_id": 706612828,
  "message": {
    "message_id": 848,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751831385,
    "text": "Do any other models have OPS?"
  }
}
2025-07-06T20:49:47: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Do any other models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T19:49:47.925Z"}
2025-07-06T20:49:47: 💾 Cached search results for: "Do any other models have OPS?"
2025-07-06T21:04:04: 📨 Webhook received: 2025-07-06T20:04:04.217Z
2025-07-06T21:04:04: 📨 Request body: {
  "update_id": 706612829,
  "message": {
    "message_id": 851,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832244,
    "text": "/reset",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T21:04:08: 📨 Webhook received: 2025-07-06T20:04:08.526Z
2025-07-06T21:04:08: 📨 Request body: {
  "update_id": 706612830,
  "message": {
    "message_id": 853,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832248,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T21:04:18: 📨 Webhook received: 2025-07-06T20:04:18.057Z
2025-07-06T21:04:18: 📨 Request body: {
  "update_id": 706612831,
  "message": {
    "message_id": 855,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832258,
    "text": "Which models have VGA?"
  }
}
2025-07-06T21:04:19: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have VGA?" {"service":"weaviate-service","timestamp":"2025-07-06T20:04:19.569Z"}
2025-07-06T21:04:19: 💾 Cached search results for: "Which models have VGA?"
2025-07-06T21:05:03: 📨 Webhook received: 2025-07-06T20:05:03.584Z
2025-07-06T21:05:03: 📨 Request body: {
  "update_id": 706612832,
  "message": {
    "message_id": 858,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832303,
    "text": "Which models dont have OPS?"
  }
}
2025-07-06T21:05:05: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models dont have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T20:05:05.871Z"}
2025-07-06T21:05:05: 💾 Cached search results for: "Which models dont have OPS?"
2025-07-06T21:08:33: 📨 Webhook received: 2025-07-06T20:08:33.606Z
2025-07-06T21:08:33: 📨 Request body: {
  "update_id": 706612833,
  "message": {
    "message_id": 861,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832513,
    "text": "Which models do have OPS?"
  }
}
2025-07-06T21:08:35: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models do have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T20:08:35.505Z"}
2025-07-06T21:08:35: 💾 Cached search results for: "Which models do have OPS?"
2025-07-06T21:11:20: 📨 Webhook received: 2025-07-06T20:11:20.532Z
2025-07-06T21:11:20: 📨 Request body: {
  "update_id": 706612834,
  "message": {
    "message_id": 864,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832680,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T21:11:28: 📨 Webhook received: 2025-07-06T20:11:28.811Z
2025-07-06T21:11:28: 📨 Request body: {
  "update_id": 706612835,
  "message": {
    "message_id": 866,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832688,
    "text": "Which models do have OPS?"
  }
}
2025-07-06T21:11:30: ⚡ Cache hit for: "Which models do have OPS?"
2025-07-06T21:11:30: [32minfo[39m: ⚡ Cache hit for search: "Which models do have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T20:11:30.128Z"}
2025-07-06T21:11:45: 📨 Webhook received: 2025-07-06T20:11:45.155Z
2025-07-06T21:11:45: 📨 Request body: {
  "update_id": 706612836,
  "message": {
    "message_id": 869,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832705,
    "text": "Which models don't have OPS?"
  }
}
2025-07-06T21:11:46: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models don't have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T20:11:46.961Z"}
2025-07-06T21:11:46: 💾 Cached search results for: "Which models don't have OPS?"
2025-07-06T21:14:26: 
Shutting down gracefully...
2025-07-06T21:14:29: 🚀 AI Product Support Agent running on port 3000
2025-07-06T21:14:29: 🌐 Web interface: http://localhost:3000
2025-07-06T21:14:29: 🤖 Telegram bot: Active
2025-07-06T21:14:29: 📊 Database: Connected
2025-07-06T21:14:29: 🧠 OpenAI: Connected
2025-07-06T21:14:29: ✅ Redis connected
2025-07-06T21:14:29: ✅ Redis connected
2025-07-06T21:14:29: 🔥 Warming up cache...
2025-07-06T21:14:29: 🔥 Warming up cache...
2025-07-06T21:14:29: ✅ Cache warmed up successfully
2025-07-06T21:14:29: ✅ Cache warmed up successfully
2025-07-06T21:14:29: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T20:14:29.522Z"}
2025-07-06T21:14:29: [32minfo[39m: ✅ Cache service initialized {"service":"weaviate-service","timestamp":"2025-07-06T20:14:29.530Z"}
2025-07-06T21:14:29: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T20:14:29.563Z"}
2025-07-06T21:14:29: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T20:14:29.565Z"}
2025-07-06T21:14:29: ✅ Telegram bot: Hybrid search initialized
2025-07-06T21:14:29: [32minfo[39m: ✅ Weaviate connection established {"service":"weaviate-service","timestamp":"2025-07-06T20:14:29.579Z"}
2025-07-06T21:14:29: [32minfo[39m: ✅ Hybrid search initialized with Weaviate {"service":"weaviate-service","timestamp":"2025-07-06T20:14:29.581Z"}
2025-07-06T21:14:29: Database connection successful
2025-07-06T21:14:29: Webhook set successfully
2025-07-06T21:14:29: Telegram bot webhook set up
2025-07-06T21:14:29: All services initialized successfully
2025-07-06T21:15:06: 📨 Webhook received: 2025-07-06T20:15:06.461Z
2025-07-06T21:15:06: 📨 Request body: {
  "update_id": 706612837,
  "message": {
    "message_id": 872,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832906,
    "text": "/start",
    "entities": [
      {
        "offset": 0,
        "length": 6,
        "type": "bot_command"
      }
    ]
  }
}
2025-07-06T21:15:13: 📨 Webhook received: 2025-07-06T20:15:13.519Z
2025-07-06T21:15:13: 📨 Request body: {
  "update_id": 706612838,
  "message": {
    "message_id": 874,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832913,
    "text": "Which models have OPS?"
  }
}
2025-07-06T21:15:14: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models have OPS?" {"service":"weaviate-service","timestamp":"2025-07-06T20:15:14.943Z"}
2025-07-06T21:15:14: 💾 Cached search results for: "Which models have OPS?"
2025-07-06T21:15:39: 📨 Webhook received: 2025-07-06T20:15:39.496Z
2025-07-06T21:15:39: 📨 Request body: {
  "update_id": 706612839,
  "message": {
    "message_id": 877,
    "from": {
      "id": 5560551341,
      "is_bot": false,
      "first_name": "Andy",
      "language_code": "en"
    },
    "chat": {
      "id": 5560551341,
      "first_name": "Andy",
      "type": "private"
    },
    "date": 1751832939,
    "text": "Which models don't have an OPS slot?"
  }
}
2025-07-06T21:15:40: [32minfo[39m: ✅ Weaviate search: 3 KB + 0 docs + 0 structured for "Which models don't have an OPS slot?" {"service":"weaviate-service","timestamp":"2025-07-06T20:15:40.865Z"}
2025-07-06T21:15:40: 💾 Cached search results for: "Which models don't have an OPS slot?"
