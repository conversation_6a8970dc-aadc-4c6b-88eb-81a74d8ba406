-- =====================================================
-- Migration: Add Critical Specification Columns
-- Date: 2025-01-06
-- Description: Add separate columns for critical specifications
--              to improve performance and AI context building
-- =====================================================

-- Add critical connectivity columns
ALTER TABLE products ADD COLUMN IF NOT EXISTS ops_support BOOLEAN DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS hdmi_ports INTEGER DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS vga_ports INTEGER DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS usb_c_ports INTEGER DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS wifi_support BOOLEAN DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS bluetooth_support BOOLEAN DEFAULT NULL;

-- Add critical software specification columns
ALTER TABLE products ADD COLUMN IF NOT EXISTS browser_support BOOLEAN DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS google_play_store BOOLEAN DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS google_edla_certified BOOLEAN DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS app_installation_method TEXT DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS screen_casting_support BOOLEAN DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS screen_recording_support BOOLEAN DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS warranty_period TEXT DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS whiteboard_application TEXT DEFAULT NULL;

-- Add constraints for data integrity
ALTER TABLE products ADD CONSTRAINT chk_hdmi_ports_range 
  CHECK (hdmi_ports IS NULL OR (hdmi_ports >= 0 AND hdmi_ports <= 10));

ALTER TABLE products ADD CONSTRAINT chk_vga_ports_range 
  CHECK (vga_ports IS NULL OR (vga_ports >= 0 AND vga_ports <= 5));

ALTER TABLE products ADD CONSTRAINT chk_usb_c_ports_range 
  CHECK (usb_c_ports IS NULL OR (usb_c_ports >= 0 AND usb_c_ports <= 5));

ALTER TABLE products ADD CONSTRAINT chk_app_installation_method 
  CHECK (app_installation_method IS NULL OR 
         app_installation_method IN ('none', 'sideloading', 'play_store', 'both'));

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_products_ops_support ON products(ops_support);
CREATE INDEX IF NOT EXISTS idx_products_hdmi_ports ON products(hdmi_ports);
CREATE INDEX IF NOT EXISTS idx_products_google_play_store ON products(google_play_store);
CREATE INDEX IF NOT EXISTS idx_products_browser_support ON products(browser_support);
CREATE INDEX IF NOT EXISTS idx_products_wifi_support ON products(wifi_support);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_products_connectivity_combo 
  ON products(ops_support, hdmi_ports, wifi_support) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_products_google_services 
  ON products(google_play_store, google_edla_certified) WHERE status = 'active';

-- Add column comments for documentation
COMMENT ON COLUMN products.ops_support IS 'Open Pluggable Specification support - critical for modular computing integration';
COMMENT ON COLUMN products.hdmi_ports IS 'Number of HDMI ports - essential for display connectivity planning';
COMMENT ON COLUMN products.vga_ports IS 'Number of VGA ports - important for legacy system compatibility';
COMMENT ON COLUMN products.usb_c_ports IS 'Number of USB-C ports - modern connectivity standard';
COMMENT ON COLUMN products.wifi_support IS 'WiFi connectivity support';
COMMENT ON COLUMN products.bluetooth_support IS 'Bluetooth connectivity support';
COMMENT ON COLUMN products.browser_support IS 'Built-in browser support - critical for web-based applications';
COMMENT ON COLUMN products.google_play_store IS 'Google Play Store availability - major factor for Android app deployment';
COMMENT ON COLUMN products.google_edla_certified IS 'Google EDLA (Enterprise Devices Licensing Agreement) certification';
COMMENT ON COLUMN products.app_installation_method IS 'How apps can be installed: none, sideloading, play_store, or both';
COMMENT ON COLUMN products.screen_casting_support IS 'Screen casting/mirroring capability';
COMMENT ON COLUMN products.screen_recording_support IS 'Screen recording capability';
COMMENT ON COLUMN products.warranty_period IS 'Warranty period information';
COMMENT ON COLUMN products.whiteboard_application IS 'Built-in whiteboard application name';

-- Create migration functions
CREATE OR REPLACE FUNCTION migrate_connectivity_data()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER := 0;
  product_record RECORD;
BEGIN
  FOR product_record IN
    SELECT id, model_number, connectivity
    FROM products
    WHERE status = 'active' AND connectivity IS NOT NULL
  LOOP
    UPDATE products SET
      ops_support = CASE
        WHEN (connectivity->>'ops')::text = 'true' THEN true
        WHEN (connectivity->>'ops')::text = 'false' THEN false
        ELSE NULL
      END,
      hdmi_ports = CASE
        WHEN connectivity->>'hdmi' IS NOT NULL THEN (connectivity->>'hdmi')::integer
        ELSE NULL
      END,
      vga_ports = CASE
        WHEN connectivity->>'vga' IS NOT NULL THEN (connectivity->>'vga')::integer
        ELSE NULL
      END,
      usb_c_ports = CASE
        WHEN connectivity->>'usb_c' IS NOT NULL THEN (connectivity->>'usb_c')::integer
        ELSE NULL
      END,
      wifi_support = CASE
        WHEN (connectivity->>'wifi')::text = 'true' THEN true
        WHEN (connectivity->>'wifi')::text = 'false' THEN false
        ELSE NULL
      END,
      bluetooth_support = CASE
        WHEN (connectivity->>'bluetooth')::text = 'true' THEN true
        WHEN (connectivity->>'bluetooth')::text = 'false' THEN false
        ELSE NULL
      END
    WHERE id = product_record.id;

    updated_count := updated_count + 1;
  END LOOP;

  RETURN updated_count;
END;
$$ LANGUAGE plpgsql;
