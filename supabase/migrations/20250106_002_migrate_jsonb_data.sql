-- =====================================================
-- Migration: Migrate JSONB Data to Columns
-- Date: 2025-01-06
-- Description: Migrate existing JSONB data to new columns
-- =====================================================

-- Create specifications migration function
CREATE OR REPLACE FUNCTION migrate_specifications_data()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER := 0;
  product_record RECORD;
BEGIN
  FOR product_record IN 
    SELECT id, model_number, specifications 
    FROM products 
    WHERE status = 'active' AND specifications IS NOT NULL
  LOOP
    UPDATE products SET
      browser_support = CASE 
        WHEN (specifications->>'browser')::text = 'true' THEN true
        WHEN (specifications->>'browser')::text = 'false' THEN false
        ELSE NULL
      END,
      google_play_store = CASE 
        WHEN (specifications->>'google_play_store')::text = 'true' THEN true
        WHEN (specifications->>'google_play_store')::text = 'false' THEN false
        ELSE NULL
      END,
      google_edla_certified = CASE 
        WHEN (specifications->>'google_edla')::text = 'true' THEN true
        WHEN (specifications->>'google_edla')::text = 'false' THEN false
        ELSE NULL
      END,
      app_installation_method = CASE 
        WHEN specifications->>'install_apps' = 'No' THEN 'none'
        WHEN specifications->>'install_apps' LIKE '%sideloading%' THEN 'sideloading'
        WHEN specifications->>'install_apps' LIKE '%Play Store%' THEN 'play_store'
        WHEN specifications->>'install_apps' LIKE '%both%' THEN 'both'
        ELSE NULL
      END,
      screen_casting_support = CASE 
        WHEN (specifications->>'screen_casting')::text = 'true' THEN true
        WHEN (specifications->>'screen_casting')::text = 'false' THEN false
        ELSE NULL
      END,
      screen_recording_support = CASE 
        WHEN (specifications->>'screen_recording')::text = 'true' THEN true
        WHEN (specifications->>'screen_recording')::text = 'false' THEN false
        ELSE NULL
      END,
      warranty_period = specifications->>'warranty',
      whiteboard_application = specifications->>'whiteboard'
    WHERE id = product_record.id;
    
    updated_count := updated_count + 1;
  END LOOP;
  
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- Execute the migration functions
DO $$
DECLARE
  connectivity_count INTEGER;
  specifications_count INTEGER;
BEGIN
  -- Migrate connectivity data
  SELECT migrate_connectivity_data() INTO connectivity_count;
  RAISE NOTICE 'Migrated connectivity data for % products', connectivity_count;
  
  -- Migrate specifications data
  SELECT migrate_specifications_data() INTO specifications_count;
  RAISE NOTICE 'Migrated specifications data for % products', specifications_count;
  
  -- Log the migration completion
  RAISE NOTICE 'Migration completed successfully';
END $$;

-- Create validation function
CREATE OR REPLACE FUNCTION validate_migration_data()
RETURNS TABLE(
  model_number TEXT,
  issue_type TEXT,
  jsonb_value TEXT,
  column_value TEXT
) AS $$
BEGIN
  -- Check OPS support consistency
  RETURN QUERY
  SELECT 
    p.model_number,
    'ops_mismatch' as issue_type,
    (p.connectivity->>'ops')::text as jsonb_value,
    p.ops_support::text as column_value
  FROM products p
  WHERE p.status = 'active' 
    AND p.connectivity IS NOT NULL
    AND p.connectivity->>'ops' IS NOT NULL
    AND (p.connectivity->>'ops')::boolean != p.ops_support;
    
  -- Check HDMI ports consistency
  RETURN QUERY
  SELECT 
    p.model_number,
    'hdmi_mismatch' as issue_type,
    (p.connectivity->>'hdmi')::text as jsonb_value,
    p.hdmi_ports::text as column_value
  FROM products p
  WHERE p.status = 'active' 
    AND p.connectivity IS NOT NULL
    AND p.connectivity->>'hdmi' IS NOT NULL
    AND (p.connectivity->>'hdmi')::integer != p.hdmi_ports;
    
  -- Check Google Play Store consistency
  RETURN QUERY
  SELECT 
    p.model_number,
    'play_store_mismatch' as issue_type,
    (p.specifications->>'google_play_store')::text as jsonb_value,
    p.google_play_store::text as column_value
  FROM products p
  WHERE p.status = 'active' 
    AND p.specifications IS NOT NULL
    AND p.specifications->>'google_play_store' IS NOT NULL
    AND (p.specifications->>'google_play_store')::boolean != p.google_play_store;
    
  -- Check browser support consistency
  RETURN QUERY
  SELECT 
    p.model_number,
    'browser_mismatch' as issue_type,
    (p.specifications->>'browser')::text as jsonb_value,
    p.browser_support::text as column_value
  FROM products p
  WHERE p.status = 'active' 
    AND p.specifications IS NOT NULL
    AND p.specifications->>'browser' IS NOT NULL
    AND (p.specifications->>'browser')::boolean != p.browser_support;
END;
$$ LANGUAGE plpgsql;

-- Run validation and display results
DO $$
DECLARE
  validation_record RECORD;
  issue_count INTEGER := 0;
BEGIN
  RAISE NOTICE 'Running migration validation...';
  
  FOR validation_record IN SELECT * FROM validate_migration_data() LOOP
    RAISE NOTICE 'VALIDATION ISSUE: % - % - JSONB: %, Column: %', 
      validation_record.model_number,
      validation_record.issue_type,
      validation_record.jsonb_value,
      validation_record.column_value;
    issue_count := issue_count + 1;
  END LOOP;
  
  IF issue_count = 0 THEN
    RAISE NOTICE 'Migration validation PASSED - no issues found';
  ELSE
    RAISE NOTICE 'Migration validation found % issues', issue_count;
  END IF;
END $$;
