{"version": 3, "file": "legacy-mode.js", "sourceRoot": "", "sources": ["../../../lib/client/legacy-mode.ts"], "names": [], "mappings": ";;;;;;AAEA,4CAAiD;AAEjD,2DAAmC;AACnC,qEAAiD;AAmBjD,MAAa,iBAAiB;IAC5B,MAAM,CAAC,mBAAmB,CAAC,SAA2B,EAAE,IAA4B;QAClF,IAAI,QAAoC,CAAC;QACzC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE,CAAC;YAChD,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAoB,CAAC;QAC1C,CAAC;QAED,iBAAiB,CAAC,aAAa,CAAC,SAAS,EAAE,IAAuB,CAAC,CAAC;QAEpE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,SAA2B,EAAE,IAAqB;QACrE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvB,iBAAiB,CAAC,aAAa,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,CACZ,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,IAAI,CAAC,CAAC;oBAC9C,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAChB,GAAG,CACN,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAAgB,EAAE,IAAkB;QAC3D,OAAO,OAAO,CAAC,sBAAsB,CAAC,CAAC;YACrC,IAAA,6BAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAClC,SAAS,CAAC;IACd,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,IAAY,EAAE,OAAgB,EAAE,IAAkB;QACtE,MAAM,cAAc,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1E,OAAO,UAAmC,GAAG,IAA4B;YACvE,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,EACtB,QAAQ,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,EACjE,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAEhD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACtD,OAAO;YACT,CAAC;YAED,OAAO;iBACJ,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;iBAC7E,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,CAA8D;IACrE,MAAM,CAAmD;IAEzD,YACE,MAAmE;QAEnE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC;QACvC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAQ,CAAC,EAAE,CAAC;YACvD,gBAAgB;YACf,IAAY,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,cAAc,CACpD,IAAI,EACJ,OAAO,EACP,IAAI,CACL,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,WAAW,CAAC,GAAG,IAA4B;QACzC,MAAM,SAAS,GAAqB,EAAE,EACpC,QAAQ,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,EACjE,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAEhD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,OAAO;aACJ,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACpC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;CACF;AA1FD,8CA0FC;AAQD,MAAM,kBAAkB;IACtB,MAAM,CAAC,cAAc,CAAC,IAAY,EAAE,OAAgB,EAAE,IAAkB;QACtE,MAAM,cAAc,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1E,OAAO,UAAoC,GAAG,IAAqB;YACjE,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;YACzB,iBAAiB,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,IAAkB;QAC/B,MAAM,KAAK,GAAG,KAAM,SAAQ,kBAAkB;SAAG,CAAC;QAElD,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAQ,CAAC,EAAE,CAAC;YACvD,gBAAgB;YACf,KAAa,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,cAAc,CAChE,IAAI,EACJ,OAAO,EACP,IAAI,CACL,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,MAAmE,EAAE,EAAE;YAC7E,OAAO,IAAI,KAAK,CAAC,MAAM,CAAoC,CAAC;QAC9D,CAAC,CAAC;IACJ,CAAC;IAEQ,MAAM,GAAG,IAAI,uBAAiB,EAAE,CAAC;IACjC,OAAO,CAA8D;IAE9E,YAAY,MAAmE;QAC7E,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,WAAW,CAAC,GAAG,IAAqB;QAClC,MAAM,SAAS,GAAqB,EAAE,CAAC;QACvC,iBAAiB,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAAC,EAAkE;QACrE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAE9D,IAAI,CAAC,EAAE,EAAE,CAAC;YACR,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,OAAO;aACJ,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;aAChE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7B,CAAC;CACF"}