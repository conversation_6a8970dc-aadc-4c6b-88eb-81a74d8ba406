{"version": 3, "file": "pool.d.ts", "sourceRoot": "", "sources": ["../../../lib/client/pool.ts"], "names": [], "mappings": ";;;AACA,OAAO,EAAW,aAAa,EAAiB,cAAc,EAAE,YAAY,EAAe,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC1J,OAAoB,EAAE,eAAe,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,MAAM,GAAG,CAAC;AAC5F,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAI3C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAgC,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AACvF,OAAO,EAA8B,qBAAqB,EAAE,6BAA6B,EAAE,MAAM,SAAS,CAAC;AAI3G,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IACvB;;;;;OAKG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,eAAe,CAAC,EAAE,6BAA6B,GAAG,qBAAqB,CAAC;IACxE;;;;;;;;OAQG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAChC;AAED,MAAM,MAAM,QAAQ,CAClB,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,EAChC,CAAC,GAAG,OAAO,IACT,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;AAEhE,MAAM,MAAM,mBAAmB,CAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,IACnC,CACF,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAC5C,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CACnD,CAAC;AAMF,qBAAa,eAAe,CAC1B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,CACrC,SAAQ,YAAY;;IAkDpB,MAAM,CAAC,MAAM,CACX,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,GAAG,EAAE,EAErC,aAAa,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,iBAAiB,CAAC,EACxF,OAAO,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC;IAwCrC;;OAEG;IACH,IAAI,WAAW,WAEd;IAID;;OAEG;IACH,IAAI,YAAY,WAEf;IAED;;OAEG;IACH,IAAI,YAAY,WAEf;IASD;;OAEG;IACH,IAAI,gBAAgB,WAEnB;IAID;;OAEG;IACH,IAAI,MAAM,YAET;IAID;;OAEG;IACH,IAAI,SAAS,YAEZ;IAGD,IAAI,eAAe,8CAElB;IAED;;;;OAIG;gBAED,aAAa,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAC/D,OAAO,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC;IAyBrC,OAAO,CAAC,KAAK,CAAQ;IACrB,OAAO,CAAC,eAAe,CAAC,CAA+B;IAEvD,kBAAkB,CAChB,OAAO,SAAS,cAAc,CAAC,YAAY,CAAC,EAC5C,YAAY,SAAS,WAAW,EAChC,OAAO,EAAE,OAAO;IA+BlB;;OAEG;IACH,eAAe,CAAC,YAAY,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY;IAI3E;;OAEG;IACH,eAAe,CAAC,WAAW,EAAE,WAAW;IAIxC;;;OAGG;IACH,IAAI;IAIE,OAAO;IAoCb,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAmEvD,cAAc,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC;IAkBhC,WAAW,CACT,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,EAC1B,OAAO,CAAC,EAAE,cAAc;IAK1B,KAAK;IASL,KAAK,qEAAc;IAEb,KAAK;IA8BX,OAAO;CAgBR"}