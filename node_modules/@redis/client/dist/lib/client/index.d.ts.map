{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../lib/client/index.ts"], "names": [], "mappings": ";;;AAAA,OAAO,QAAQ,MAAM,aAAa,CAAC;AACnC,OAAoB,EAAE,kBAAkB,EAAE,MAAM,UAAU,CAAC;AAC3D,OAAO,EAA+B,mBAAmB,EAA+E,MAAM,UAAU,CAAC;AACzJ,OAA2B,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AACtE,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAK3C,OAAO,EAAe,UAAU,EAAE,cAAc,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAC3G,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAiB,cAAc,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,cAAc,EAAoB,MAAM,eAAe,CAAC;AACpR,OAAgC,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AACvF,OAAO,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AAE3D,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAClE,OAAO,EAAqB,qBAAqB,EAAE,MAAM,eAAe,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAmB,MAAM,QAAQ,CAAC;AAC3D,OAAO,EAAE,qBAAqB,EAAoC,MAAM,kCAAkC,CAAC;AAC3G,OAAO,EAAwB,qBAAqB,EAAE,uBAAuB,EAAE,MAAM,SAAS,CAAC;AAC/F,OAAO,EAAsB,aAAa,EAAE,MAAM,UAAU,CAAC;AAI7D,MAAM,WAAW,kBAAkB,CACjC,CAAC,SAAS,YAAY,GAAG,YAAY,EACrC,CAAC,SAAS,cAAc,GAAG,cAAc,EACzC,CAAC,SAAS,YAAY,GAAG,YAAY,EACrC,IAAI,SAAS,YAAY,GAAG,YAAY,EACxC,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,aAAa,SAAS,kBAAkB,GAAG,kBAAkB,CAC7D,SAAQ,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACtC;;;OAGG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,MAAM,CAAC,EAAE,aAAa,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,mBAAmB,CAAC;IAC1C;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,cAAc,CAAC,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;IAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,eAAe,CAAC,EAAE,uBAAuB,GAAG,qBAAqB,CAAC;IAClE;;OAEG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,KAAK,YAAY,CACf,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACC,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,gBAAgB,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CACzF,CAAC;AAEJ,KAAK,WAAW,CACd,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACC,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACjE;CACF,CAAC;AAEJ,KAAK,aAAa,CAChB,CAAC,SAAS,cAAc,EACxB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACC,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACjE;CACF,CAAC;AAEJ,KAAK,WAAW,CACd,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACC,CAAC,IAAI,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CAC3D,CAAC;AAEJ,MAAM,MAAM,qBAAqB,CAC/B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,IACnC,CACA,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,GAChC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAClC,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACpC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CACnC,CAAC;AAEJ,MAAM,MAAM,eAAe,CACzB,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,IACnC,CACA,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACxC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CACnD,CAAC;AAMJ,UAAU,mBAAmB;IAC3B,MAAM,CAAC,EAAE,aAAa,CAAC;CACxB;AAED,MAAM,MAAM,eAAe,CAAC,YAAY,SAAS,WAAW,GAAG,WAAW,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC,iBAAiB,EAAE,YAAY,CAAC,KAAK,OAAO,CAAC;AAExJ,MAAM,CAAC,OAAO,OAAO,WAAW,CAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,CAChC,SAAQ,YAAY;;IAmDpB,MAAM,CAAC,OAAO,CACZ,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IA4BzC,MAAM,CAAC,MAAM,CACX,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,EACrC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAIvE,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,kBAAkB;IAuDhD,OAAO,CAAC,KAAK,CAAQ;IACrB,OAAO,CAAC,eAAe,CAAC,CAA+B;IAQvD,IAAI,eAAe,wCAElB;IAGD,IAAI,OAAO,IAAI,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,SAAS,CAE3D;IAED,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,IAAI,cAAc,YAEjB;IAED,IAAI,WAAW,WAEd;IAED,IAAI,UAAU,YAEb;IAED;;;;OAIG;IACH,IAAI,YAAY,IAAI,OAAO,CAE1B;IAED;;;;OAIG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM;gBAIb,OAAO,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAkErE;;OAEG;IACH,OAAO,CAAC,cAAc,CAUrB;IAoOD,kBAAkB,CAChB,OAAO,SAAS,cAAc,CAAC,YAAY,CAAC,EAC5C,YAAY,SAAS,WAAW,EAChC,OAAO,EAAE,OAAO;IAYlB,OAAO,CAAC,oBAAoB;IAmB5B;;OAEG;IACH,eAAe,CAAC,YAAY,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY;IAI3E;;OAEG;IACH,eAAe,CAAC,WAAW,EAAE,WAAW;IAIxC;;OAEG;IACH,IAAI;IAIJ;;OAEG;IACH,MAAM,IAAI,qBAAqB;IAM/B;;OAEG;IACH,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC;IAO9C,SAAS,CACP,EAAE,SAAS,YAAY,GAAG,CAAC,EAC3B,EAAE,SAAS,cAAc,GAAG,CAAC,EAC7B,EAAE,SAAS,YAAY,GAAG,CAAC,EAC3B,KAAK,SAAS,YAAY,GAAG,IAAI,EACjC,aAAa,SAAS,WAAW,GAAG,YAAY,EAChD,SAAS,CAAC,EAAE,OAAO,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;IAQrE,OAAO;IAKb;;OAEG;IACG,eAAe,CACnB,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,aAAa,EACrB,cAAc,EAAE,cAAc,CAAC,YAAY,CAAC,GAAG,SAAS,EACxD,cAAc,EAAE,cAAc,GAAG,SAAS;IAmB5C;;OAEG;IACG,cAAc,CAClB,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,aAAa,EACrB,OAAO,EAAE,cAAc,GAAG,SAAS,EACnC,cAAc,EAAE,cAAc,GAAG,SAAS;IAoB5C,WAAW,CAAC,CAAC,GAAG,UAAU,EACxB,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC,EAClC,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,CAAC,CAAC;IAYP,MAAM,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKvC,MAAM,OALW,MAAM,KAAG,QAAQ,IAAI,CAAC,CAKlB;IASrB,SAAS,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACjC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC,GACb,OAAO,CAAC,IAAI,CAAC;IAWhB,SAAS,wCAdG,MAAM,GAAG,MAAM,MAAM,CAAC,8DAG/B,QAAQ,IAAI,CAAC,CAWW;IAE3B,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACnC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,UAAU,CAAC,EAAE,CAAC,GACb,OAAO,CAAC,IAAI,CAAC;IAWhB,WAAW,yCAdE,MAAM,GAAG,MAAM,MAAM,CAAC,2EAGhC,QAAQ,IAAI,CAAC,CAWe;IAE/B,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EAClC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC,GACb,OAAO,CAAC,IAAI,CAAC;IAWhB,UAAU,wCAdE,MAAM,GAAG,MAAM,MAAM,CAAC,8DAG/B,QAAQ,IAAI,CAAC,CAWa;IAE7B,YAAY,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACpC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,UAAU,CAAC,EAAE,CAAC,GACb,OAAO,CAAC,IAAI,CAAC;IAWhB,YAAY,yCAdC,MAAM,GAAG,MAAM,MAAM,CAAC,2EAGhC,QAAQ,IAAI,CAAC,CAWiB;IAEjC,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EAClC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC,GACb,OAAO,CAAC,IAAI,CAAC;IAWhB,UAAU,wCAdE,MAAM,GAAG,MAAM,MAAM,CAAC,8DAG/B,QAAQ,IAAI,CAAC,CAWa;IAE7B,YAAY,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACpC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,UAAU,CAAC,EAAE,CAAC,GACb,OAAO,CAAC,IAAI,CAAC;IAWhB,YAAY,yCAdC,MAAM,GAAG,MAAM,MAAM,CAAC,2EAGhC,QAAQ,IAAI,CAAC,CAWiB;IAE3B,KAAK,CAAC,GAAG,EAAE,qBAAqB;IAQtC,KAAK,QARY,qBAAqB,0MAQnB;IAEb,OAAO;IAMb,OAAO,6MAAgB;IAEvB,kBAAkB,CAAC,IAAI,EAAE,UAAU;IAInC,4BAA4B,CAC1B,IAAI,EAAE,UAAU,EAChB,OAAO,EAAE,MAAM,EACf,SAAS,EAAE,gBAAgB;IAO7B,qBAAqB,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,mBAAmB;IA2BtE;;OAEG;IACG,gBAAgB,CACpB,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC,EACxC,UAAU,CAAC,EAAE,MAAM;IAuBrB;;OAEG;IACG,aAAa,CACjB,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC,EACxC,UAAU,CAAC,EAAE,MAAM;IAsDrB,KAAK;IASL,KAAK,qEAAc;IAEZ,YAAY,CACjB,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAClD,OAAO,CAAC,EAAE,WAAW,GAAG,mBAAmB;IAUtC,aAAa,CAClB,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAClD,GAAG,EAAE,aAAa,EAClB,OAAO,CAAC,EAAE,iBAAiB,GAAG,mBAAmB;;;;IAU5C,mBAAmB,CACxB,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAClD,GAAG,EAAE,aAAa,EAClB,OAAO,CAAC,EAAE,iBAAiB,GAAG,mBAAmB;IAU5C,qBAAqB,CAC1B,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAClD,GAAG,EAAE,aAAa,EAClB,OAAO,CAAC,EAAE,iBAAiB,GAAG,mBAAmB;IAU5C,aAAa,CAClB,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAClD,GAAG,EAAE,aAAa,EAClB,OAAO,CAAC,EAAE,iBAAiB,GAAG,mBAAmB;IAU5C,aAAa,CAClB,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAClD,GAAG,EAAE,aAAa,EAClB,OAAO,CAAC,EAAE,iBAAiB,GAAG,mBAAmB;;;;IAU7C,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,YAAY,CAAC;IASrD,OAAO,aATiB,gBAAgB,YAAY,CAAC,mBAS9B;IAEvB;;OAEG;IACG,KAAK;IAeX;;;;OAIG;IACH,YAAY;IA2BZ;;OAEG;IACH,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC;IAWvB,IAAI,QAXI,QAAQ,MAAM,CAAC,CAWN;IAEjB;;OAEG;IACH,UAAU;IAIV;;OAEG;IACH,KAAK;IAwBL;;OAEG;IACH,OAAO;IASP,GAAG;IAIH,KAAK;CAGN"}