{"version": 3, "file": "socket.js", "sourceRoot": "", "sources": ["../../../lib/client/socket.ts"], "names": [], "mappings": ";;;;;AAAA,6CAAiD;AACjD,wDAA2B;AAC3B,wDAA2B;AAC3B,sCAAiJ;AACjJ,mDAAkD;AAoDlD,MAAqB,WAAY,SAAQ,0BAAY;IAC1C,UAAU,CAAC;IACX,eAAe,CAAC;IAChB,kBAAkB,CAAC;IACnB,cAAc,CAAC;IACf,cAAc,CAAC;IAExB,OAAO,CAA8B;IAErC,OAAO,GAAG,KAAK,CAAC;IAEhB,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,QAAQ,GAAG,KAAK,CAAC;IAEjB,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,gBAAgB,GAAG,KAAK,CAAC;IAEzB,YAAY,GAAG,CAAC,CAAC;IAEjB,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,YAAY,SAA+B,EAAE,OAA4B;QACvE,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,OAAO,EAAE,cAAc,IAAI,IAAI,CAAC;QACvD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,aAAa,CAAC;IAC/C,CAAC;IAED,wBAAwB,CAAC,OAA4B;QACnD,MAAM,QAAQ,GAAG,OAAO,EAAE,iBAAiB,CAAC;QAC5C,IAAI,QAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACvD,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC;QACxB,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gBACxB,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;oBACzC,IAAI,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,YAAY,KAAK,CAAC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;wBACpF,MAAM,IAAI,SAAS,CAAC,oEAAoE,OAAO,UAAU,CAAC,CAAC;oBAC7G,CAAC;oBACD,OAAO,OAAO,CAAC;gBACjB,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;oBACxB,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACvC,CAAC;IAED,oBAAoB,CAAC,OAA4B;QAC/C,MAAM;QACN,IAAI,OAAO,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;YAC1B,MAAM,YAAY,GAA0B;gBAC1C,GAAG,OAAO;gBACV,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,IAAI;gBAC3B,8GAA8G;gBAC9G,iCAAiC;gBACjC,mBAAmB;gBACnB,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,IAAI;gBACjC,8GAA8G;gBAC9G,iCAAiC;gBACjC,mBAAmB;gBACnB,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;gBACrC,8GAA8G;gBAC9G,iCAAiC;gBACjC,mBAAmB;gBACnB,qBAAqB,EAAE,OAAO,EAAE,qBAAqB,IAAI,IAAI;gBAC7D,OAAO,EAAE,SAAS;gBAClB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf,CAAC;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO,kBAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBACnC,CAAC;gBACD,KAAK,EAAE,eAAe;aACvB,CAAC;QACJ,CAAC;QAED,MAAM;QACN,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;YACjC,MAAM,YAAY,GAA0B;gBAC1C,GAAG,OAAO;gBACV,OAAO,EAAE,SAAS;gBAClB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf,CAAC;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO,kBAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;gBAC5C,CAAC;gBACD,KAAK,EAAE,SAAS;aACjB,CAAC;QACJ,CAAC;QAED,MAAM;QACN,MAAM,YAAY,GAA0B;YAC1C,GAAG,OAAO;YACV,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,IAAI;YAC3B,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,IAAI;YACjC,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;YACrC,qBAAqB,EAAE,OAAO,EAAE,qBAAqB,IAAI,IAAI;YAC7D,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,IAAI;SACf,CAAC;QACF,OAAO;YACL,MAAM;gBACJ,OAAO,kBAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAC5C,CAAC;YACD,KAAK,EAAE,SAAS;SACjB,CAAC;IACJ,CAAC;IAED,gBAAgB,CAAC,OAAe,EAAE,KAAY;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,IAAI,OAAO,YAAY,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,OAAO,IAAI,+BAAsB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,GAAG,CAAC;YACF,IAAI,CAAC;gBACH,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAErB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC1B,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBACvB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;oBACzB,MAAM,GAAG,CAAC;gBACZ,CAAC;gBACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,GAAY,CAAC,CAAC;gBAC/D,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;oBAChC,MAAM,OAAO,CAAC;gBAChB,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACxB,MAAM,IAAA,qBAAU,EAAC,OAAO,CAAC,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IAC3C,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QAE5C,IAAI,SAAS,CAAC;QACd,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,SAAS,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,+BAAsB,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAClC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QAED,MAAM,IAAA,kBAAI,EAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE9C,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE;gBAC1B,MAAM,CAAC,OAAO,CAAC,IAAI,2BAAkB,CAAC,IAAI,CAAC,cAAe,CAAC,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzC,CAAC;QAED,MAAM;aACH,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;aAC9C,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;YACxB,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;gBAAE,OAAO;YACjE,IAAI,CAAC,cAAc,CAAC,IAAI,sCAA6B,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC;aACD,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACrC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QAE/C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,cAAc,CAAC,GAAU;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAExB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,QAAQ;YAAE,OAAO;QAE5F,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;YACzB,oDAAoD;QACtD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,QAAgD;QACpD,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,KAAK,MAAM,OAAO,IAAI,IAAI,EAAE,CAAC;gBAC3B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB;gBAAE,MAAM;QAC5C,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,IAAI,CAAI,EAAoB;QAChC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,MAAM,KAAK,GAAG,MAAM,EAAE,EAAE,CAAC;QACzB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,aAAa;QACX,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED,GAAG;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;IACtB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,wBAAwB,CAAC,OAAe,EAAE,KAAc;QACtD,kDAAkD;QAClD,IAAI,KAAK,YAAY,2BAAkB,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,+CAA+C;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAC/C,wFAAwF;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC;QAExD,OAAO,KAAK,GAAG,MAAM,CAAC;IACxB,CAAC;CACF;AAzTD,8BAyTC"}