{"version": 3, "file": "parser.d.ts", "sourceRoot": "", "sources": ["../../../lib/client/parser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AAEzE,MAAM,WAAW,aAAa;IAC5B,SAAS,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;IACxC,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;IACnC,QAAQ,EAAE,aAAa,GAAG,SAAS,CAAC;IACpC,QAAQ,EAAE,OAAO,CAAC;IAElB,IAAI,EAAE,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK,OAAO,CAAC;IAChD,YAAY,EAAE,CAAC,IAAI,EAAE,qBAAqB,KAAK,OAAO,CAAC;IACvD,sBAAsB,EAAE,CAAC,IAAI,EAAE,qBAAqB,KAAK,OAAO,CAAC;IACjE,kBAAkB,EAAE,CAAC,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,OAAO,CAAC;IAC9D,OAAO,EAAE,CAAC,GAAG,EAAE,aAAa,KAAK,OAAO,CAAC;IACzC,QAAQ,EAAE,CAAC,IAAI,EAAE,qBAAqB,KAAK,OAAO,CAAC;IACnD,cAAc,EAAE,CAAC,IAAI,EAAE,qBAAqB,KAAK,OAAO,CAAC;CAC1D;AAED,qBAAa,kBAAmB,YAAW,aAAa;;IAGtD,QAAQ,EAAE,OAAO,CAAC;IAElB,IAAI,SAAS,oBAEZ;IAED,IAAI,IAAI,oBAEP;IAED,IAAI,QAAQ,kBAEX;IAED,IAAI,QAAQ,WASX;IAED,IAAI,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,aAAa,CAAC;IAIjC,YAAY,CAAC,IAAI,EAAE,qBAAqB;IAUxC,sBAAsB,CAAC,IAAI,EAAE,qBAAqB;IASlD,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;IAU1C,OAAO,CAAC,GAAG,EAAE,aAAa;IAK1B,cAAc,CAAC,IAAI,EAAE,qBAAqB;IAS1C,QAAQ,CAAC,IAAI,EAAE,qBAAqB;CASrC"}