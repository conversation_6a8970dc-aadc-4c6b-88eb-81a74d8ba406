{"version": 3, "file": "multi-command.d.ts", "sourceRoot": "", "sources": ["../../../lib/cluster/multi-command.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,aAAa,CAAC;AACnC,OAA0B,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AACvH,OAAO,EAAE,oBAAoB,EAAE,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAA8B,WAAW,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAGjP,OAAO,EAAE,IAAI,EAAE,MAAM,kCAAkC,CAAC;AAExD,KAAK,gBAAgB,CACnB,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,OAAO,EACjB,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,4BAA4B,CAChF;IAAC,GAAG,OAAO;IAAE,oBAAoB,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,YAAY,CAAC;CAAC,EACvE,CAAC,EACD,CAAC,EACD,CAAC,EACD,IAAI,EACJ,YAAY,CACb,CAAC;AAEF,KAAK,YAAY,CACf,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CAC3G,CAAC;AAEF,KAAK,WAAW,CACd,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACnF;CACF,CAAC;AAEF,KAAK,aAAa,CAChB,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACnF;CACF,CAAC;AAEF,KAAK,WAAW,CACd,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CAC7E,CAAC;AAEF,MAAM,MAAM,4BAA4B,CACtC,OAAO,SAAS,KAAK,CAAC,GAAG,CAAC,EAC1B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B,CACF,wBAAwB,CAAC,OAAO,CAAC,GACjC,YAAY,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAClD,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACjD,aAAa,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACnD,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAClD,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,CAChC,QAAQ,EAAE,aAAa,GAAG,SAAS,EACnC,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC,KACrC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAE7B,MAAM,CAAC,OAAO,OAAO,wBAAwB,CAAC,OAAO,GAAG,EAAE;;IAoFxD,MAAM,CAAC,MAAM,CACX,CAAC,SAAS,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAC9C,CAAC,SAAS,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAChD,CAAC,SAAS,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAC9C,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;gBAoBvC,YAAY,EAAE,mBAAmB,EACjC,eAAe,EAAE,mBAAmB,EACpC,OAAO,EAAE,aAAa,GAAG,SAAS,EAClC,WAAW,CAAC,EAAE,WAAW;IAgB3B,UAAU,CACR,QAAQ,EAAE,aAAa,GAAG,SAAS,EACnC,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,IAAI,EAAE,gBAAgB,EACtB,cAAc,CAAC,EAAE,cAAc;IAoB3B,IAAI,CAAC,CAAC,SAAS,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,EAAE,cAAc,UAAQ;IAYhF,IAAI,sGAAa;IAEjB,SAAS,CAAC,cAAc,UAAQ;IAI1B,cAAc,CAAC,CAAC,SAAS,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC;IAYlE,mBAAmB;CAGpB"}