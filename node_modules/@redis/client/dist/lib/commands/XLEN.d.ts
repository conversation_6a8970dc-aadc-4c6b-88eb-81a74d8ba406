import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
/**
 * Command for getting the length of a stream
 */
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the XLEN command to get the number of entries in a stream
     *
     * @param parser - The command parser
     * @param key - The stream key
     * @returns The number of entries inside the stream
     * @see https://redis.io/commands/xlen/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=XLEN.d.ts.map