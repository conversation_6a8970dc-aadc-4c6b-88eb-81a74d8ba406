import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
export interface XSetIdOptions {
    /** added in 7.0 */
    ENTRIESADDED?: number;
    /** added in 7.0 */
    MAXDELETEDID?: RedisArgument;
}
declare const _default: {
    readonly IS_READ_ONLY: false;
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, lastId: RedisArgument, options?: XSetIdOptions) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=XSETID.d.ts.map