require('dotenv').config();
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');

// Import services
const database = require('./config/database');
const telegramBot = require('./services/telegramBot');
const chatService = require('./services/chatService');
const knowledgeBase = require('./services/knowledgeBase');
const documentService = require('./services/documentService');
const troubleshootingService = require('./services/troubleshootingService');
const openai = require('./config/openai');

// Import routes
const webRoutes = require('./routes/web');
const apiRoutes = require('./routes/api');
const weaviateRoutes = require('./routes/weaviate');

class AIProductSupportAgent {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });
    this.port = process.env.PORT || 3000;
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketIO();
    this.initializeServices();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(express.static(path.join(__dirname, '../public')));
    this.app.set('view engine', 'html');
    this.app.set('views', path.join(__dirname, '../views'));
  }

  setupRoutes() {
    this.app.use('/', webRoutes);
    this.app.use('/api', apiRoutes);
    this.app.use('/api', weaviateRoutes);
    
    // Telegram webhook with logging
    this.app.post('/webhook', (req, res, next) => {
      console.log(`📨 Webhook received: ${new Date().toISOString()}`);
      console.log(`📨 Request body:`, JSON.stringify(req.body, null, 2));
      next();
    }, telegramBot.getWebhookMiddleware());
    
    // File upload route
    this.app.post('/upload', documentService.getMulterConfig().single('file'), async (req, res) => {
      try {
        if (!req.file) {
          return res.status(400).json({ success: false, message: 'No file uploaded' });
        }

        const processedFile = await documentService.processUploadedFile(req.file);
        
        res.json({ 
          success: true, 
          message: 'File uploaded successfully',
          file: {
            id: processedFile.id,
            originalName: processedFile.original_name,
            size: processedFile.file_size,
            type: processedFile.file_type
          }
        });
      } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({ success: false, message: error.message });
      }
    });

    // Serve file content route
    this.app.get('/files/:id', async (req, res) => {
      try {
        const document = await documentService.getDocumentById(req.params.id);
        if (!document) {
          return res.status(404).json({ success: false, message: 'Document not found' });
        }
        
        // Set appropriate headers for file download
        res.setHeader('Content-Type', document.file_type);
        res.setHeader('Content-Disposition', `attachment; filename="${document.original_name}"`);
        
        // Send the actual file from disk (ensure absolute path)
        const absolutePath = path.isAbsolute(document.file_path)
          ? document.file_path
          : path.resolve(document.file_path);

        res.sendFile(absolutePath);
      } catch (error) {
        console.error('File serve error:', error);
        res.status(500).json({ success: false, message: error.message });
      }
    });

    // Delete file route
    this.app.delete('/files/:id', async (req, res) => {
      try {
        await documentService.deleteDocument(req.params.id);
        res.json({ success: true, message: 'File deleted successfully' });
      } catch (error) {
        console.error('Delete error:', error);
        res.status(500).json({ success: false, message: error.message });
      }
    });

    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        services: {
          database: 'connected',
          openai: 'connected',
          telegram: 'connected'
        }
      });
    });
  }

  setupSocketIO() {
    this.io.on('connection', (socket) => {
      console.log('User connected:', socket.id);

      socket.on('initSession', async (data) => {
        try {
          const session = await chatService.getOrCreateSession(data.userId, data.platform);
          socket.join(session.id);
          socket.emit('sessionCreated', { sessionId: session.id });
        } catch (error) {
          console.error('Session initialization error:', error);
          socket.emit('error', { message: 'Failed to initialize session' });
        }
      });

      socket.on('message', async (data) => {
        try {
          const { message, sessionId } = data;
          
          // Save user message
          await chatService.saveMessage(sessionId, message, 'user');
          
          // Analyze intent
          const intent = await openai.analyzeUserIntent(message);
          
          let response;
          
          // Check if user is in troubleshooting flow
          const troubleshootingStatus = await troubleshootingService.getTroubleshootingStatus(sessionId);
          
          if (troubleshootingStatus) {
            // Continue troubleshooting flow
            response = await troubleshootingService.processTroubleshootingResponse(sessionId, message);
          } else if (intent.intent === 'troubleshooting') {
            // Start new troubleshooting flow
            response = await troubleshootingService.initiateTroubleshooting(sessionId, message, intent);
          } else {
            // Regular conversation with enhanced context
            const userId = sessionId.split('_')[1]; // Extract user ID from session
            const context = await knowledgeBase.getRelevantContext(message, intent, userId, 'web');
            const conversationHistory = await chatService.getRecentMessages(sessionId, 5);
            
            const messages = [
              ...conversationHistory.map(msg => ({
                role: msg.sender === 'user' ? 'user' : 'assistant',
                content: msg.message
              })),
              { role: 'user', content: message }
            ];
            
            const systemPrompt = `${openai.getSystemPrompt()}

===== ENHANCED KNOWLEDGE BASE CONTEXT (PRIORITY SOURCE) =====
${context}
===== END KNOWLEDGE BASE CONTEXT =====

User Intent: ${intent.intent}
Category: ${intent.category || 'General'}
Urgency: ${intent.urgency}

ENHANCED INSTRUCTIONS:
- Use conversation context to provide personalized responses
- Avoid suggesting solutions the user has already tried
- Reference their device model when known
- Provide progressive troubleshooting when appropriate
- Use clear formatting for web interface

IMPORTANT: Use the knowledge base context above as your PRIMARY and PREFERRED source of information. The context may include conversation history, comparative analysis, and previous attempts - use this to avoid redundant suggestions.`;

            response = await openai.generateResponse(messages, systemPrompt);
          }
          
          // Save assistant response
          await chatService.saveMessage(sessionId, response, 'assistant');
          
          // Send response to client
          socket.emit('message', { message: response });
          
        } catch (error) {
          console.error('Message processing error:', error);
          socket.emit('error', { message: 'Failed to process message' });
        }
      });

      socket.on('clearChat', async (data) => {
        try {
          const { sessionId } = data;
          await chatService.createNewSession(sessionId.split('_')[1], 'web');
          socket.emit('sessionCleared');
        } catch (error) {
          console.error('Clear chat error:', error);
          socket.emit('error', { message: 'Failed to clear chat' });
        }
      });

      socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
      });
    });
  }

  async initializeServices() {
    try {
      // Initialize database
      await database.initialize();
      
      // Set up Telegram bot only if token is provided
      if (process.env.TELEGRAM_BOT_TOKEN && process.env.TELEGRAM_BOT_TOKEN !== 'your_telegram_bot_token_here') {
        if (process.env.TELEGRAM_WEBHOOK_URL) {
          await telegramBot.setWebhook(process.env.TELEGRAM_WEBHOOK_URL);
          console.log('Telegram bot webhook set up');
        } else {
          console.log('Starting Telegram bot in polling mode...');
          await telegramBot.startPolling();
        }
      } else {
        console.log('Telegram bot disabled - no valid token provided');
      }
      
      console.log('All services initialized successfully');
    } catch (error) {
      console.error('Service initialization error:', error);
      console.log('Continuing in demo mode...');
    }
  }

  async start() {
    try {
      this.server.listen(this.port, () => {
        console.log(`🚀 AI Product Support Agent running on port ${this.port}`);
        console.log(`🌐 Web interface: http://localhost:${this.port}`);
        console.log(`🤖 Telegram bot: ${process.env.TELEGRAM_BOT_TOKEN ? 'Active' : 'Not configured'}`);
        console.log(`📊 Database: ${process.env.SUPABASE_URL ? 'Connected' : 'Not configured'}`);
        console.log(`🧠 OpenAI: ${process.env.OPENAI_API_KEY ? 'Connected' : 'Not configured'}`);
      });
    } catch (error) {
      console.error('Server start error:', error);
      process.exit(1);
    }
  }

  async stop() {
    try {
      await telegramBot.stop();
      this.server.close();
      console.log('Server stopped gracefully');
    } catch (error) {
      console.error('Server stop error:', error);
    }
  }
}

// Initialize and start the application
const app = new AIProductSupportAgent();

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nShutting down gracefully...');
  await app.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nShutting down gracefully...');
  await app.stop();
  process.exit(0);
});

// Start the application
app.start().catch(error => {
  console.error('Application start error:', error);
  process.exit(1);
});

module.exports = app;