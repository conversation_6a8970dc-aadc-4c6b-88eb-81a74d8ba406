/**
 * Intelligent Context Builder
 * Systematically builds AI context from structured data based on query intent
 */

class IntelligentContextBuilder {
  constructor(supabase) {
    this.supabase = supabase;
    
    // Define which data fields are relevant for different query types
    this.fieldMappings = {
      // Operating system and software queries
      os_software: {
        keywords: ['tizen', 'android', 'os', 'operating system', 'apps', 'software', 'browser', 'play store', 'edla', 'screen recording', 'recording', 'screen casting', 'casting'],
        fields: ['operating_system', 'os_version', 'built_in_apps', 'specifications'],
        includeSpecFields: ['browser', 'google_edla', 'google_play_store', 'install_apps', 'screen_casting', 'screen_recording']
      },
      
      // Hardware and connectivity queries
      hardware_connectivity: {
        keywords: ['ops', 'hdmi', 'vga', 'usb', 'ports', 'connectivity', 'wifi', 'bluetooth', 'touch', 'resolution', 'brightness', 'contrast', 'nits'],
        fields: ['connectivity', 'touch_points', 'touch_technology', 'resolution', 'display_size', 'brightness_nits', 'contrast_ratio', 'aspect_ratio'],
        includeSpecFields: []
      },
      
      // Physical specifications and installation
      physical_specs: {
        keywords: ['size', 'weight', 'dimensions', 'mount', 'vesa', 'installation', 'physical'],
        fields: ['dimensions', 'weight_kg', 'vesa_mount', 'display_size', 'aspect_ratio'],
        includeSpecFields: []
      },
      
      // Power and environment
      power_environment: {
        keywords: ['power', 'watts', 'consumption', 'temperature', 'electrical', 'energy'],
        fields: ['power_consumption_watts', 'power_supply', 'operating_temp_range'],
        includeSpecFields: []
      },
      
      // Business and use cases
      business_use_case: {
        keywords: ['target', 'market', 'education', 'corporate', 'meeting', 'classroom', 'use case', 'application', 'warranty', 'support', 'whiteboard', 'price', 'cost', 'launch'],
        fields: ['target_market', 'product_type', 'specifications', 'launch_date', 'price_range'],
        includeSpecFields: ['warranty', 'whiteboard']
      },
      
      // Product comparison and models
      product_comparison: {
        keywords: ['models', 'compare', 'difference', 'which', 'all', 'list', 'available'],
        fields: ['model_number', 'brand', 'product_line', 'display_size', 'operating_system', 'resolution', 'target_market'],
        includeSpecFields: []
      }
    };
  }

  /**
   * Analyze query and determine what structured data is needed
   */
  analyzeDataNeeds(userMessage) {
    const message = userMessage.toLowerCase();
    const neededCategories = [];
    
    // Check each category for keyword matches
    Object.entries(this.fieldMappings).forEach(([category, config]) => {
      const hasMatch = config.keywords.some(keyword => 
        message.includes(keyword.toLowerCase())
      );
      
      if (hasMatch) {
        neededCategories.push(category);
      }
    });
    
    // Always include basic product info for model-related queries
    if (message.match(/\b(model|product|specification|spec)\b/)) {
      neededCategories.push('product_comparison');
    }
    
    return neededCategories;
  }

  /**
   * Build comprehensive structured data context
   */
  async buildStructuredContext(userMessage, detectedModel = null) {
    try {
      const dataNeeds = this.analyzeDataNeeds(userMessage);
      
      if (dataNeeds.length === 0) {
        return ''; // No structured data needed
      }

      // Get all products or specific product
      let products;
      if (detectedModel) {
        const { data, error } = await this.supabase
          .from('products')
          .select('*')
          .eq('model_number', detectedModel.toUpperCase())
          .eq('status', 'active')
          .single();
        
        if (error) throw error;
        products = [data];
      } else {
        const { data, error } = await this.supabase
          .from('products')
          .select('*')
          .eq('status', 'active')
          .order('model_number');
        
        if (error) throw error;
        products = data;
      }

      if (!products || products.length === 0) {
        return '';
      }

      let context = '=== STRUCTURED PRODUCT DATABASE ===\\n';
      
      // Build context for each data category needed
      dataNeeds.forEach(category => {
        const config = this.fieldMappings[category];
        context += this.buildCategoryContext(category, config, products, userMessage);
      });
      
      context += '\\nIMPORTANT: Use ONLY the product information listed above. Do not mention any models or specifications not explicitly provided.\\n\\n';
      
      return context;
      
    } catch (error) {
      console.error('Error building structured context:', error);
      return '';
    }
  }

  /**
   * Build context for a specific data category
   */
  buildCategoryContext(category, config, products, userMessage) {
    let context = `\\n--- ${category.toUpperCase().replace('_', ' ')} ---\\n`;
    
    switch (category) {
      case 'os_software':
        return this.buildOSContext(products, userMessage);
      
      case 'hardware_connectivity':
        return this.buildHardwareContext(products, userMessage);
        
      case 'physical_specs':
        return this.buildPhysicalContext(products);
        
      case 'power_environment':
        return this.buildPowerContext(products);
        
      case 'business_use_case':
        return this.buildBusinessContext(products);
        
      case 'product_comparison':
        return this.buildComparisonContext(products);
        
      default:
        return '';
    }
  }

  buildOSContext(products, userMessage) {
    let context = '\\nOPERATING SYSTEM & SOFTWARE FEATURES:\\n';

    // Group by OS
    const osGroups = {};
    products.forEach(p => {
      const os = p.operating_system || 'Unknown';
      if (!osGroups[os]) osGroups[os] = [];
      osGroups[os].push(p);
    });

    Object.keys(osGroups).sort().forEach(os => {
      context += `${os} models (${osGroups[os].length} models):\\n`;
      osGroups[os].forEach(p => {
        context += `• ${p.model_number} (${p.display_size}) - ${os}\\n`;

        // Add detailed software specifications
        if (p.specifications) {
          const specs = p.specifications;

          // Browser support
          if (specs.browser !== undefined) {
            context += `  - Browser: ${specs.browser ? 'Yes (built-in)' : 'No'}\\n`;
          }

          // Google Play Store and EDLA
          if (specs.google_play_store !== undefined) {
            context += `  - Google Play Store: ${specs.google_play_store ? 'Yes' : 'No'}\\n`;
          }
          if (specs.google_edla !== undefined) {
            context += `  - Google EDLA Certified: ${specs.google_edla ? 'Yes' : 'No'}\\n`;
          }

          // App installation capabilities
          if (specs.install_apps) {
            context += `  - Install Apps: ${specs.install_apps}\\n`;
          }

          // Screen features
          if (specs.screen_casting !== undefined) {
            context += `  - Screen Casting: ${specs.screen_casting ? 'Yes' : 'No'}\\n`;
          }
          if (specs.screen_recording !== undefined) {
            context += `  - Screen Recording: ${specs.screen_recording ? 'Yes' : 'No'}\\n`;
          }

          // Whiteboard functionality
          if (specs.whiteboard) {
            context += `  - Whiteboard App: ${specs.whiteboard}\\n`;
          }

          // Warranty information
          if (specs.warranty) {
            context += `  - Warranty: ${specs.warranty}\\n`;
          }
        }

        context += '\\n';
      });
    });

    return context;
  }

  buildHardwareContext(products, userMessage) {
    let context = '\\nHARDWARE & CONNECTIVITY SPECIFICATIONS:\\n';

    products.forEach(p => {
      context += `${p.model_number} (${p.display_size}):\\n`;

      // Display specifications
      context += `  Display: ${p.resolution || 'N/A'}`;
      if (p.aspect_ratio) context += ` ${p.aspect_ratio}`;
      if (p.brightness_nits) context += `, ${p.brightness_nits} nits`;
      if (p.contrast_ratio) context += `, ${p.contrast_ratio} contrast`;
      context += '\\n';

      // Touch specifications
      if (p.touch_points) {
        context += `  Touch: ${p.touch_points}-point ${p.touch_technology || 'touch'}\\n`;
      }

      // Connectivity details
      if (p.connectivity) {
        const conn = p.connectivity;
        const ports = [];
        const unavailablePorts = [];

        // Check all possible port types
        if (conn.hdmi && conn.hdmi > 0) {
          ports.push(`${conn.hdmi} HDMI`);
        } else if (conn.hdmi === 0) {
          unavailablePorts.push('HDMI');
        }

        if (conn.vga && conn.vga > 0) {
          ports.push(`${conn.vga} VGA`);
        } else if (conn.vga === 0) {
          unavailablePorts.push('VGA');
        }

        if (conn.usb_c && conn.usb_c > 0) ports.push(`${conn.usb_c} USB-C`);
        if (conn.usb && conn.usb > 0) ports.push(`${conn.usb} USB`);
        if (conn.displayport && conn.displayport > 0) ports.push(`${conn.displayport} DisplayPort`);
        if (conn.ops) ports.push('OPS slot');
        if (conn.ethernet) ports.push('Ethernet');

        const wireless = [];
        if (conn.wifi) wireless.push('WiFi');
        if (conn.bluetooth) wireless.push('Bluetooth');

        if (ports.length > 0) context += `  Ports: ${ports.join(', ')}\\n`;
        if (wireless.length > 0) context += `  Wireless: ${wireless.join(', ')}\\n`;

        // Include unavailable ports if the query specifically asks about them
        if (unavailablePorts.length > 0 && userMessage) {
          const queryLower = userMessage.toLowerCase();
          const mentionsUnavailable = unavailablePorts.some(port =>
            queryLower.includes(port.toLowerCase())
          );
          if (mentionsUnavailable) {
            context += `  Not available: ${unavailablePorts.join(', ')}\\n`;
          }
        }
      }

      context += '\\n';
    });

    return context;
  }

  buildPhysicalContext(products) {
    let context = '\\nPHYSICAL SPECIFICATIONS:\\n';

    products.forEach(p => {
      context += `${p.model_number}: ${p.display_size} ${p.product_type}\\n`;

      // Physical dimensions
      if (p.dimensions) {
        if (typeof p.dimensions === 'object') {
          const dims = [];
          if (p.dimensions.width) dims.push(`W: ${p.dimensions.width}`);
          if (p.dimensions.height) dims.push(`H: ${p.dimensions.height}`);
          if (p.dimensions.depth) dims.push(`D: ${p.dimensions.depth}`);
          if (dims.length > 0) context += `  Dimensions: ${dims.join(', ')}\\n`;
        } else {
          context += `  Dimensions: ${p.dimensions}\\n`;
        }
      }

      // Weight and mounting
      if (p.weight_kg) context += `  Weight: ${p.weight_kg}kg\\n`;
      if (p.vesa_mount) context += `  VESA Mount: ${p.vesa_mount}\\n`;
      if (p.aspect_ratio) context += `  Aspect Ratio: ${p.aspect_ratio}\\n`;

      context += '\\n';
    });

    return context;
  }

  buildPowerContext(products) {
    let context = '\\nPOWER & ENVIRONMENTAL SPECIFICATIONS:\\n';

    products.forEach(p => {
      context += `${p.model_number} (${p.display_size}):\\n`;

      // Power specifications
      if (p.power_consumption_watts) {
        context += `  Power Consumption: ${p.power_consumption_watts}W\\n`;
      }
      if (p.power_supply) {
        context += `  Power Supply: ${p.power_supply}\\n`;
      }

      // Environmental specifications
      if (p.operating_temp_range) {
        context += `  Operating Temperature: ${p.operating_temp_range}\\n`;
      }

      // If no power data available, show basic info
      if (!p.power_consumption_watts && !p.power_supply && !p.operating_temp_range) {
        context += `  Power specifications not available\\n`;
      }

      context += '\\n';
    });

    return context;
  }

  buildBusinessContext(products) {
    let context = '\\nBUSINESS & USE CASE INFORMATION:\\n';

    // Group by target market
    const marketGroups = {};
    products.forEach(p => {
      const markets = p.target_market ? p.target_market.split(',').map(m => m.trim()) : ['General'];
      markets.forEach(market => {
        if (!marketGroups[market]) marketGroups[market] = [];
        marketGroups[market].push(p);
      });
    });

    Object.keys(marketGroups).sort().forEach(market => {
      context += `${market} Applications:\\n`;
      marketGroups[market].forEach(p => {
        context += `• ${p.model_number} (${p.product_type}, ${p.display_size})\\n`;
        context += `  Target Market: ${p.target_market || 'General'}\\n`;

        // Business specifications
        if (p.specifications) {
          if (p.specifications.warranty) context += `  Warranty: ${p.specifications.warranty}\\n`;
          if (p.specifications.whiteboard) context += `  Whiteboard: ${p.specifications.whiteboard}\\n`;
        }

        // Additional business info
        if (p.launch_date) context += `  Launch Date: ${p.launch_date}\\n`;
        if (p.price_range) context += `  Price Range: ${p.price_range}\\n`;

        context += '\\n';
      });
    });

    return context;
  }

  buildComparisonContext(products) {
    let context = '\\nCOMPLETE PRODUCT LINEUP:\\n';

    products.forEach(p => {
      context += `${p.model_number}: ${p.brand} ${p.product_line} ${p.display_size} ${p.product_type}\\n`;
      context += `  OS: ${p.operating_system || 'N/A'}, Resolution: ${p.resolution || 'N/A'}`;
      if (p.touch_points) context += `, Touch: ${p.touch_points}-point`;
      context += '\\n';

      if (p.target_market) context += `  Target: ${p.target_market}\\n`;

      // Explicit OPS status (always show for clarity)
      if (p.connectivity) {
        const hasOPS = p.connectivity.ops === true;
        context += `  OPS Support: ${hasOPS ? 'YES' : 'NO'}\\n`;
      }

      // Key differentiators including connectivity
      const keyFeatures = [];

      // Add software features
      if (p.specifications) {
        if (p.specifications.google_play_store) keyFeatures.push('Play Store');
        if (p.specifications.google_edla) keyFeatures.push('EDLA');
        if (p.specifications.screen_recording) keyFeatures.push('Recording');
      }

      // Add connectivity features
      if (p.connectivity) {
        if (p.connectivity.ops) keyFeatures.push('OPS');
        if (p.connectivity.vga && p.connectivity.vga > 0) keyFeatures.push('VGA');
      }

      if (keyFeatures.length > 0) {
        context += `  Features: ${keyFeatures.join(', ')}\\n`;
      }

      context += '\\n';
    });

    return context;
  }
}

module.exports = IntelligentContextBuilder;