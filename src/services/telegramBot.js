const { Telegraf } = require('telegraf');
const openai = require('../config/openai');
const knowledgeBase = require('./knowledgeBase');
const HybridSearchService = require('./hybridSearchService');
const chatService = require('./chatService');
const responseFormatter = require('./responseFormatter');
const conversationMemory = require('./conversationMemory');
const TelegramUX = require('../utils/telegramUX');
const cacheService = require('./cacheService');
const productService = require('./productService');
const userDeviceService = require('./userDeviceService');
const supportIssueService = require('./supportIssueService');
const IntelligentContextBuilderV2 = require('./intelligentContextBuilderV2');

class TelegramBotService {
  constructor() {
    this.bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN, {
      telegram: {
        timeout: 25000, // 25 second timeout instead of default 90
        retries: 2,
        agent: null
      }
    });
    this.hybridSearch = new HybridSearchService();
    this.ux = new TelegramUX();
    this.contextBuilder = new IntelligentContextBuilderV2(knowledgeBase.supabase);
    this.setupErrorHandling();
    this.setupCommands();
    this.setupMiddleware();
    this.initializeServices();
  }

  async initializeServices() {
    try {
      await this.hybridSearch.initialize();
      console.log('✅ Telegram bot: Hybrid search initialized');
    } catch (error) {
      console.error('❌ Telegram bot: Failed to initialize hybrid search:', error.message);
    }
  }

  setupErrorHandling() {
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      // Don't exit the process, just log the error
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      // Don't exit the process, just log the error
    });

    // Handle bot errors, especially network timeouts
    this.bot.catch((err, ctx) => {
      console.error('Bot error:', err.message || err);
      
      // Don't respond to network timeout errors to avoid duplicate messages
      if (err.code === 'ETIMEOUT' || err.type === 'request-timeout' || err.message.includes('timeout')) {
        console.log('Network timeout - not responding to avoid duplicate messages');
        return;
      }
      
      // For other errors, try to send a safe response
      try {
        if (ctx?.reply) {
          ctx.reply('⚠️ Something went wrong. Please try again.').catch(() => {
            console.log('Failed to send error message - likely network issue');
          });
        }
      } catch (replyError) {
        console.log('Error sending error message:', replyError.message);
      }
    });
  }

  // Safe reply function with timeout protection
  async safeReply(ctx, message, options = {}) {
    const timeout = options.timeout || 20000; // 20 second default timeout
    try {
      return await Promise.race([
        ctx.reply(message, options),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Reply timeout')), timeout)
        )
      ]);
    } catch (error) {
      if (error.message === 'Reply timeout' || error.type === 'request-timeout') {
        console.log('Safe reply timeout - message may have been sent');
        return null;
      }
      throw error;
    }
  }

  setupMiddleware() {
    this.bot.use(async (ctx, next) => {
      const userId = ctx.from.id.toString();
      const username = ctx.from.username || ctx.from.first_name;
      
      ctx.session = await chatService.getOrCreateSession(userId, 'telegram', {
        username,
        first_name: ctx.from.first_name,
        last_name: ctx.from.last_name
      });
      
      await next();
    });
  }

  setupCommands() {
    this.bot.start(async (ctx) => {
      try {
        // Clear old cached response to use new welcome flow
        await cacheService.clearCache('/start');
        
        // Enhanced welcome message with quick actions
        const welcomeMessage = `🤖 Hi! I'm your Samsung Display Assistant.

Quick setup - what brings you here today?

💡 **Pro tip**: Type your model (WA65C, WM75B, etc.) for instant specs!`;

        const welcomeKeyboard = [
          [{ text: '🔍 I have a specific model', callback_data: 'have_model' }],
          [{ text: '📱 Help me find my model', callback_data: 'find_model' }],
          [{ text: '❓ General questions', callback_data: 'general_help' }],
          [{ text: '📚 Browse all models', callback_data: 'browse_models' }]
        ];
        
        // Cache the new welcome response for future use
        const welcomeResponse = {
          text: welcomeMessage,
          reply_markup: {
            inline_keyboard: welcomeKeyboard
          }
        };

        await cacheService.cacheQuickResponse('/start', welcomeResponse);

        return this.safeReply(ctx, welcomeMessage, {
          reply_markup: {
            inline_keyboard: welcomeKeyboard
          }
        });
      } catch (error) {
        console.error('Start command error:', error);
        return this.safeReply(ctx, 'Welcome! How can I help you today?');
      }
    });

    this.bot.command('help', async (ctx) => {
      try {
        // Try cached response first
        const cachedResponse = await cacheService.getCachedQuickResponse('/help');
        if (cachedResponse) {
          return await this.ux.handleQuickResponse(ctx, '/help', cachedResponse);
        }
        
        // Fallback to original message
        const helpMessage = `📋 Available Commands:

/start - Welcome message
/help - Show this help menu
/categories - View product categories
/devices - View your registered devices
/mydevice - View your primary device
/issues - View your support issues
/reset - Reset conversation
/support - Get support contact info

💡 How to use:
Simply describe your issue or ask a question about our interactive display products. I'll guide you through troubleshooting steps or provide the information you need.

Examples:
• "My display won't turn on"
• "How do I calibrate the touch screen?"
• "What are the display specifications?"`;
        
        return this.safeReply(ctx, helpMessage);
      } catch (error) {
        console.error('Help command error:', error);
        return this.safeReply(ctx, 'Available commands: /start, /help, /categories, /reset');
      }
    });

    this.bot.command('categories', async (ctx) => {
      try {
        // Try cached response first
        const cachedResponse = await cacheService.getCachedQuickResponse('/categories');
        if (cachedResponse) {
          return await this.ux.handleQuickResponse(ctx, '/categories', cachedResponse);
        }
        
        // Fallback to database query
        const categories = await knowledgeBase.supabase
          .from('product_categories')
          .select('*');
        
        if (categories.data && categories.data.length > 0) {
          const categoryList = categories.data
            .map(cat => `• ${cat.name}: ${cat.description}`)
            .join('\n');
          
          const response = `📦 Product Categories:\n\n${categoryList}`;
          
          // Cache the response for next time
          await cacheService.cacheQuickResponse('/categories', {
            text: response,
            parse_mode: 'Markdown'
          });
          
          return this.safeReply(ctx, response);
        } else {
          return this.safeReply(ctx, 'No product categories found.');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        return this.safeReply(ctx, 'Sorry, I encountered an error fetching categories.');
      }
    });

    this.bot.command('reset', async (ctx) => {
      try {
        const userId = ctx.from.id.toString();
        await chatService.createNewSession(userId, 'telegram', {
          username: ctx.from.username || ctx.from.first_name,
          first_name: ctx.from.first_name,
          last_name: ctx.from.last_name
        });
        
        return ctx.reply('🔄 Conversation reset! How can I help you today?');
      } catch (error) {
        console.error('Error resetting conversation:', error);
        return ctx.reply('Sorry, I encountered an error resetting the conversation.');
      }
    });

    this.bot.command('support', (ctx) => {
      return ctx.reply(`
📞 Additional Support Options:

If you need further assistance beyond what I can provide:
• Email: <EMAIL>
• Phone: 1-800-SUPPORT
• Live Chat: Available on our website
• Business Hours: Mon-Fri 9AM-6PM EST

I'm here 24/7 to help with common issues and questions!
      `);
    });

    this.bot.command('devices', async (ctx) => {
      try {
        const userId = ctx.from.id.toString();
        const userDevices = await userDeviceService.getUserDevices(userId, 'telegram');
        
        const deviceList = userDeviceService.formatDeviceList(userDevices);
        
        if (userDevices.length === 0) {
          return this.safeReply(ctx, '📱 No devices registered yet.\n\nJust mention your device model (like "WA65F" or "CVTE-IFP-75") in a message and I\'ll automatically track it for you!');
        }
        
        return this.safeReply(ctx, deviceList);
      } catch (error) {
        console.error('Error getting user devices:', error);
        return this.safeReply(ctx, 'Sorry, I encountered an error retrieving your devices.');
      }
    });

    this.bot.command('mydevice', async (ctx) => {
      try {
        const userId = ctx.from.id.toString();
        const primaryDevice = await userDeviceService.getUserPrimaryDevice(userId, 'telegram');
        
        if (!primaryDevice) {
          const recentDevice = await userDeviceService.getUserMostRecentDevice(userId, 'telegram');
          if (recentDevice) {
            return this.safeReply(ctx, `🔍 You don't have a primary device set, but your most recent device is:\n\n${userDeviceService.formatDeviceContext(recentDevice)}\n\nWould you like to set this as your primary device?`);
          } else {
            return this.safeReply(ctx, '📱 No devices found. Mention your device model in a message and I\'ll track it for you!');
          }
        }
        
        const deviceInfo = productService.formatProductInfo(primaryDevice.products, true);
        return this.safeReply(ctx, `⭐ Your Primary Device:\n\n${deviceInfo}\n\n${userDeviceService.formatDeviceContext(primaryDevice)}`);
      } catch (error) {
        console.error('Error getting primary device:', error);
        return this.safeReply(ctx, 'Sorry, I encountered an error retrieving your primary device.');
      }
    });

    this.bot.command('issues', async (ctx) => {
      try {
        const userId = ctx.from.id.toString();
        const activeIssues = await supportIssueService.getUserActiveIssues(userId);
        
        if (activeIssues.length === 0) {
          return this.safeReply(ctx, '🔄 No active support issues found.\n\nWhen you describe a problem, I\'ll automatically create a support ticket to track our conversation!');
        }
        
        let issueList = '🎟️ Your Active Support Issues:\n\n';
        activeIssues.forEach((issue, index) => {
          issueList += `${index + 1}. ${issue.issue_category}\n`;
          issueList += `   🔍 ${issue.issue_subcategory}\n`;
          issueList += `   ⚠️ ${issue.severity} priority\n`;
          if (issue.products) {
            issueList += `   📱 ${issue.products.model_number}\n`;
          }
          issueList += `   🕰️ ${new Date(issue.created_at).toLocaleDateString()}\n\n`;
        });
        
        return this.safeReply(ctx, issueList);
      } catch (error) {
        console.error('Error getting user issues:', error);
        return this.safeReply(ctx, 'Sorry, I encountered an error retrieving your support issues.');
      }
    });

    this.bot.on('document', async (ctx) => {
      try {
        const document = ctx.message.document;
        const supportedTypes = ['application/pdf', 'text/plain', 'application/msword'];
        
        if (!supportedTypes.includes(document.mime_type)) {
          return ctx.reply('Sorry, I can only process PDF, DOC, and TXT files.');
        }
        
        await ctx.reply('📄 I received your document. Let me process it...');
        
        const fileLink = await ctx.telegram.getFileLink(document.file_id);
        
        return ctx.reply(`Document received: ${document.file_name}. I'll analyze it and use the information to better assist you with your questions.`);
      } catch (error) {
        console.error('Error processing document:', error);
        return ctx.reply('Sorry, I encountered an error processing your document.');
      }
    });

    this.bot.on('text', async (ctx) => {
      try {
        const userMessage = ctx.message.text;
        const userId = ctx.from.id.toString();
        
        // Immediate UX improvement: Start typing indicator
        await this.ux.sendTyping(ctx, 1000);
        
        // Ensure session exists
        if (!ctx.session) {
          ctx.session = await chatService.getOrCreateSession(userId, 'telegram', {
            username: ctx.from.username || ctx.from.first_name,
            first_name: ctx.from.first_name,
            last_name: ctx.from.last_name
          });
        }
        
        await chatService.saveMessage(ctx.session.id, userMessage, 'user');
        
        // Extract product model from message
        const detectedModel = productService.extractProductModel(userMessage);
        let productInfo = null;

        if (detectedModel) {
          productInfo = await productService.getProductByModel(detectedModel);
          if (productInfo) {
            console.log(`🔍 Detected product: ${productInfo.model_number} (${productInfo.brand})`);
            // Track user's device
            await userDeviceService.trackUserDevice(userId, 'telegram', detectedModel, {
              notes: `Mentioned in: "${userMessage.substring(0, 100)}..."`
            });

            // Provide instant model confirmation with quick actions
            if (this.isModelOnlyQuery(userMessage)) {
              return this.handleModelQuery(ctx, productInfo, userMessage);
            }
          }
        } else if (this.needsModelInfo(userMessage)) {
          // Ask for model when it's needed but not provided
          return this.askForModel(ctx, userMessage);
        } else if (this.isManualRequest(userMessage)) {
          // Handle manual/document requests specifically
          return this.handleManualRequest(ctx, userMessage, detectedModel);
        }
        
        // Get user's device context
        const deviceContext = await userDeviceService.getDeviceContext(userId, 'telegram', detectedModel);
        
        // Analyze intent quickly
        const intent = await openai.analyzeUserIntent(userMessage);
        
        // Send immediate acknowledgment to prevent webhook timeout
        await this.ux.sendAcknowledgment(ctx, userMessage, intent);
        
        // Process the rest asynchronously to avoid webhook timeouts
        setImmediate(() => {
          this.processMessageAsync(ctx, userMessage, intent, detectedModel, productInfo, deviceContext, userId);
        });
        
        // Return early to prevent webhook timeout
        return;
        
        // Use progressive search with UX enhancements
        let context = '';
        try {
          // Start longer typing indicator for search
          await this.ux.sendTyping(ctx, 8000);
          
          // Progressive search with timeout handling
          const searchFunction = async () => {
            return await this.hybridSearch.searchContent(userMessage, {
              limit: 5,
              category: intent.category,
              deviceModel: detectedModel,
              includeDocuments: true
            });
          };
          
          const searchResults = await this.ux.handleComplexQuery(
            ctx, 
            userMessage, 
            searchFunction, 
            intent
          );
          
          // Add product and device context if detected
          if (productInfo) {
            context += '=== PRODUCT CONTEXT ===\n';
            context += productService.formatProductInfo(productInfo, true);
            context += '\n\n';
          }
          
          if (deviceContext) {
            context += '=== USER DEVICE CONTEXT ===\n';
            context += deviceContext.context;
            context += '\n\n';
          }
          
          if (currentIssue) {
            context += '=== SUPPORT ISSUE CONTEXT ===\n';
            context += `Issue Type: ${currentIssue.issue_category} - ${currentIssue.issue_subcategory}\n`;
            context += `Severity: ${currentIssue.severity}\n`;
            context += `Issue ID: ${currentIssue.id}\n\n`;
          }
          
          // Add intelligent structured data context
          try {
            const structuredContext = await this.contextBuilder.buildStructuredContext(userMessage, detectedModel);
            if (structuredContext) {
              context += structuredContext;
            }
          } catch (error) {
            console.error('Failed to build structured context:', error);
          }

          // Format context from Weaviate results
          if (searchResults.knowledgeBase.length > 0) {
            context += '=== KNOWLEDGE BASE RESULTS ===\n';
            searchResults.knowledgeBase.forEach((item, i) => {
              context += `${i + 1}. ${item.title}\n`;
              context += `Category: ${item.category || 'N/A'}\n`;
              context += `Content: ${item.content.substring(0, 500)}...\n`;
              context += `Score: ${item.search_score || 'N/A'}\n\n`;
            });
          }
          
          if (searchResults.documents.length > 0) {
            context += '=== DOCUMENT RESULTS ===\n';
            searchResults.documents.forEach((item, i) => {
              context += `${i + 1}. ${item.filename}\n`;
              context += `Content: ${item.content.substring(0, 300)}...\n\n`;
            });

            // Send documents as downloadable links immediately
            await this.sendDocumentDownloads(ctx, searchResults.documents, detectedModel);
          } else {
            // Fallback: Check for documents directly in Supabase if hybrid search finds none
            try {
              const { data: documents, error } = await knowledgeBase.supabase
                .from('documents')
                .select('*')
                .order('original_name');

              if (!error && documents && documents.length > 0) {
                // Filter documents that might be relevant to the query
                const relevantDocs = documents.filter(doc => {
                  const name = (doc.original_name || '').toLowerCase();
                  const queryLower = userMessage.toLowerCase();

                  return name.includes('manual') ||
                         name.includes('guide') ||
                         queryLower.includes('manual') ||
                         queryLower.includes('guide') ||
                         queryLower.includes('document');
                });

                if (relevantDocs.length > 0) {
                  context += '=== AVAILABLE DOCUMENTS ===\n';
                  relevantDocs.slice(0, 3).forEach((doc, i) => {
                    context += `${i + 1}. ${doc.original_name}\n`;
                    context += `Type: ${doc.file_type}\n\n`;
                  });

                  // Send documents as downloadable links
                  await this.sendDocumentDownloads(ctx, relevantDocs.slice(0, 3), detectedModel);
                }
              }
            } catch (docError) {
              console.error('Error fetching documents from Supabase:', docError);
            }
          }
          
          if (!context) {
            context = 'No relevant knowledge base content found for this query.';
          } else {
            context += `\nSearch Source: ${searchResults.source} (${searchResults.searchType})`;
          }
          
        } catch (error) {
          console.error('❌ Weaviate search failed, falling back to old method:', error.message);
          // Fallback to original knowledge base search
          context = await knowledgeBase.getRelevantContext(userMessage, intent, userId, 'telegram');
        }
        
        const conversationHistory = await chatService.getRecentMessages(ctx.session.id, 5);
        
        const messages = [
          ...conversationHistory.map(msg => ({
            role: msg.sender === 'user' ? 'user' : 'assistant',
            content: msg.message
          })),
          {
            role: 'user',
            content: userMessage
          }
        ];
        
        const systemPrompt = `${openai.getSystemPrompt()}

===== ENHANCED KNOWLEDGE BASE CONTEXT (PRIORITY SOURCE) =====
${context}
===== END KNOWLEDGE BASE CONTEXT =====

User Intent: ${intent.intent}
Category: ${intent.category || 'General'}
Urgency: ${intent.urgency}
${detectedModel ? `\nDetected Product Model: ${detectedModel}` : ''}
${productInfo ? `\nProduct Information: ${productInfo.brand} ${productInfo.model_number} - ${productInfo.display_size} ${productInfo.product_type}` : ''}
${deviceContext ? `\nUser Device Context: ${deviceContext.device.products.model_number} (${deviceContext.device.is_primary ? 'Primary Device' : 'Secondary Device'})` : ''}
${currentIssue ? `\nSupport Issue: ${currentIssue.issue_category} - ${currentIssue.issue_subcategory} (${currentIssue.severity} priority)` : ''}

ENHANCED INSTRUCTIONS:
- Use conversation context to provide personalized responses
- Avoid suggesting solutions the user has already tried
- Reference their device model when known (${detectedModel || 'not detected'})
- Provide progressive troubleshooting when appropriate
- Use formatting optimized for Telegram mobile interface
${productInfo ? '- Use the detected product specifications to provide more accurate support' : ''}

IMPORTANT: Use the knowledge base context above as your PRIMARY and PREFERRED source of information. The context may include conversation history and previous attempts - use this to avoid redundant suggestions.`;

        // Add timeout to prevent webhook timeouts
        const responsePromise = openai.generateResponse(messages, systemPrompt);
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Response timeout')), 25000) // 25 second timeout
        );
        
        const response = await Promise.race([responsePromise, timeoutPromise]);
        
        // Determine response type for appropriate formatting
        let responseType = 'general';
        if (intent.intent === 'troubleshooting') {
          responseType = 'troubleshooting';
        } else if (conversationHistory.length === 0) {
          responseType = 'initial_response';
        } else if (response.toLowerCase().includes('try') || response.toLowerCase().includes('step')) {
          responseType = 'solution_provided';
        }
        
        // Get session data for contextual formatting
        const session = await conversationMemory.getOrCreateSession(userId, 'telegram');
        
        // Format response with rich formatting and quick replies
        const formattedResponse = responseFormatter.formatTelegramResponse(
          response, 
          responseType, 
          session.session_data
        );
        
        // Apply additional contextual enhancements
        if (session.session_data) {
          formattedResponse.text = responseFormatter.formatContextualResponse(
            formattedResponse.text, 
            session.session_data
          );
        }
        
        await chatService.saveMessage(ctx.session.id, response, 'assistant');
        
        // Use enhanced response formatting with quick actions
        const quickActions = this.ux.createQuickActions(intent, { 
          searchResults: searchResults.knowledgeBase.length 
        });
        
        // Merge quick actions with existing reply markup
        const enhancedReplyMarkup = {
          ...formattedResponse.reply_markup,
          ...quickActions.reply_markup
        };
        
        // Send chunked response for better UX
        return await this.ux.sendChunkedResponse(ctx, formattedResponse.text);
      } catch (error) {
        console.error('Error processing message:', error);
        
        if (error.message === 'Response timeout') {
          return ctx.reply('⏱️ The request is taking longer than expected. Please try again with a shorter question or try again in a moment.');
        }
        
        // Use enhanced error formatting
        const errorMessage = this.ux.formatErrorMessage(error, userMessage);
        return ctx.reply(errorMessage);
      }
    });

    // Handle callback queries from inline keyboards
    this.bot.on('callback_query', async (ctx) => {
      try {
        const callbackData = ctx.callbackQuery.data;
        const userId = ctx.from.id.toString();
        
        // Get session data for context
        const session = await conversationMemory.getOrCreateSession(userId, 'telegram');
        
        // Handle the callback
        const response = responseFormatter.handleButtonCallback(callbackData, userId, 'telegram');
        
        // Answer the callback query to remove loading state
        await ctx.answerCbQuery();

        // Handle new welcome flow callbacks
        if (callbackData === 'have_model') {
          return ctx.reply(`Great! Please tell me your model number (like WA65C, WM75B, etc.) and I'll get your specifications instantly.

💡 **Tip**: You can find your model number on a label on the back or side of your display.`);
        } else if (callbackData === 'find_model') {
          const identifyKeyboard = [
            [{ text: '📱 Android displays (WA series)', callback_data: 'android_series' }],
            [{ text: '💻 Tizen displays (WM series)', callback_data: 'tizen_series' }],
            [{ text: '📷 Send photo of label', callback_data: 'photo_help' }],
            [{ text: '📏 Tell me the size', callback_data: 'size_help' }]
          ];

          return ctx.reply(`I'll help you identify your model! What do you know about your display?`, {
            reply_markup: { inline_keyboard: identifyKeyboard }
          });
        } else if (callbackData === 'general_help') {
          const generalKeyboard = [
            [{ text: '📊 Compare models', callback_data: 'compare_models' }],
            [{ text: '🔌 Connectivity options', callback_data: 'connectivity_info' }],
            [{ text: '📱 Software features', callback_data: 'software_info' }],
            [{ text: '💼 Business solutions', callback_data: 'business_info' }]
          ];

          return ctx.reply(`What would you like to learn about Samsung interactive displays?`, {
            reply_markup: { inline_keyboard: generalKeyboard }
          });
        } else if (callbackData === 'browse_models') {
          return this.handleBrowseModels(ctx);
        } else if (callbackData === 'android_series') {
          return this.handleAndroidSeriesHelp(ctx);
        } else if (callbackData === 'tizen_series') {
          return this.handleTizenSeriesHelp(ctx);
        } else if (callbackData === 'photo_help') {
          return ctx.reply(`📷 **Photo Identification Help**

Please take a clear photo of the label on your display (usually on the back or side) and send it to me. I'll help identify your model from the label.

The label typically shows:
• Model number (like WA65C, WM75B)
• Serial number
• Power specifications

💡 **Can't find the label?** Try looking:
• On the back of the display
• On the right or left side
• Near the power connection`);
        } else if (callbackData === 'size_help') {
          const sizeKeyboard = [
            [{ text: '55"', callback_data: 'size_55' }, { text: '65"', callback_data: 'size_65' }],
            [{ text: '75"', callback_data: 'size_75' }, { text: '85"', callback_data: 'size_85' }],
            [{ text: '📏 Other size', callback_data: 'size_other' }]
          ];

          return ctx.reply(`What size is your display?`, {
            reply_markup: { inline_keyboard: sizeKeyboard }
          });
        } else if (callbackData.startsWith('specs_')) {
          return this.handleSpecsCallback(ctx, callbackData);
        } else if (callbackData.startsWith('model_')) {
          return this.handleModelCallback(ctx, callbackData);
        } else if (callbackData.startsWith('size_')) {
          return this.handleSizeCallback(ctx, callbackData);
        } else if (callbackData === 'browse_manuals') {
          return this.handleBrowseManuals(ctx);
        } else if (callbackData === 'search_by_model') {
          return ctx.reply('Please tell me your model number (like WA65C, WM75B, etc.) and I\'ll find the specific manuals for that model.');
        } else if (callbackData === 'contact_support') {
          return ctx.reply('📞 **Support Contact Information**\n\nFor manual downloads and additional support:\n• Email: <EMAIL>\n• Phone: 1-800-SUPPORT\n• Live Chat: Available on our website\n• Business Hours: Mon-Fri 9AM-6PM EST');
        }

        // Handle existing callbacks
        else if (callbackData === 'solution_worked') {
          // Mark current issue as resolved in session
          await conversationMemory.updateSessionContext(session.id, {
            current_issue: null,
            conversation_context: [{
              timestamp: new Date().toISOString(),
              action: 'solution_confirmed_working',
              query: 'User confirmed solution worked'
            }]
          });
          
          const followUpResponse = responseFormatter.formatTelegramResponse(
            response + "\n\nIs there anything else I can help you with?",
            'follow_up'
          );
          
          return ctx.reply(followUpResponse.text, {
            parse_mode: followUpResponse.parse_mode,
            reply_markup: followUpResponse.reply_markup
          });
        } else if (callbackData === 'solution_failed') {
          // Track failed solution and suggest alternatives
          await conversationMemory.updateSessionContext(session.id, {
            attempted_solutions: session.session_data.attempted_solutions || [],
            conversation_context: [{
              timestamp: new Date().toISOString(),
              action: 'solution_failed',
              query: 'User reported solution did not work'
            }]
          });
          
          return ctx.reply("I understand that didn't work. Let me suggest some alternative approaches. Can you describe exactly what happened when you tried the previous steps?");
        } else if (callbackData.startsWith('issue_')) {
          // Handle issue type selection
          const issueType = callbackData.replace('issue_', '');
          await conversationMemory.updateSessionContext(session.id, {
            issue_category: issueType + '_issues',
            conversation_context: [{
              timestamp: new Date().toISOString(),
              action: 'issue_category_selected',
              query: `User selected ${issueType} issue category`
            }]
          });
          
          return ctx.reply(`Got it! You're having ${issueType} issues. Please describe the specific problem you're experiencing.`);
        } else {
          // Default response for other callbacks
          return ctx.reply(response);
        }
      } catch (error) {
        console.error('Error handling callback query:', error);
        await ctx.answerCbQuery('Sorry, something went wrong.');
        return ctx.reply('I encountered an error processing your selection. Please try again.');
      }
    });

    this.bot.catch((err, ctx) => {
      console.error('Bot error:', err);
      return ctx.reply('Sorry, something went wrong. Please try again.');
    });
  }

  // Async processing method to handle heavy operations without webhook timeout
  async processMessageAsync(ctx, userMessage, intent, detectedModel, productInfo, deviceContext, userId) {
    try {
      // Create support issue if this appears to be a support request
      let currentIssue = null;
      if (intent.intent === 'troubleshooting' || intent.intent === 'support_request') {
        const issueClassification = supportIssueService.classifyIssue(userMessage);
        currentIssue = await supportIssueService.createIssue(ctx.session.id, userId, {
          productModel: detectedModel,
          userDeviceId: deviceContext?.device?.id,
          issueCategory: issueClassification.category,
          issueSubcategory: issueClassification.subcategory,
          issueDescription: userMessage,
          severity: issueClassification.severity
        });
        
        if (currentIssue) {
          console.log(`🎟️ Created support issue: ${currentIssue.issue_category} - ${currentIssue.issue_subcategory}`);
        }
      }

      // Start typing indicator
      await this.ux.sendTyping(ctx, 5000);
      
      // Simplified search with shorter timeout
      let searchResults = { knowledgeBase: [], documents: [], source: 'none' };
      try {
        searchResults = await Promise.race([
          this.hybridSearch.searchContent(userMessage, {
            limit: 3,
            category: intent.category,
            deviceModel: detectedModel,
            includeDocuments: true
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Search timeout')), 8000)
          )
        ]);
      } catch (searchError) {
        console.error('❌ Search failed:', searchError.message);
        searchResults = { knowledgeBase: [], documents: [], source: 'fallback' };
      }
      
      // Build context
      let context = '';
      if (productInfo) {
        context += '=== PRODUCT CONTEXT ===\n';
        context += productService.formatProductInfo(productInfo, true);
        context += '\n\n';
      }
      
      if (deviceContext) {
        context += '=== USER DEVICE CONTEXT ===\n';
        context += deviceContext.context;
        context += '\n\n';
      }
      
      if (currentIssue) {
        context += '=== SUPPORT ISSUE CONTEXT ===\n';
        context += `Issue Type: ${currentIssue.issue_category} - ${currentIssue.issue_subcategory}\n`;
        context += `Severity: ${currentIssue.severity}\n`;
        context += `Issue ID: ${currentIssue.id}\n\n`;
      }
      
      // Add intelligent structured data context
      try {
        const structuredContext = await this.contextBuilder.buildStructuredContext(userMessage, detectedModel);
        if (structuredContext) {
          context += structuredContext;
        }
      } catch (error) {
        console.error('Failed to build structured context:', error);
      }
      
      if (searchResults.knowledgeBase.length > 0) {
        context += '=== KNOWLEDGE BASE RESULTS ===\n';
        searchResults.knowledgeBase.forEach((item, i) => {
          context += `${i + 1}. ${item.title}\n`;
          context += `Content: ${item.content.substring(0, 400)}...\n\n`;
        });
        context += `Search Source: ${searchResults.source}\n\n`;
      } else {
        context += 'No specific knowledge base results found. Providing general assistance.\n\n';
      }

      // Handle documents in async processing
      if (searchResults.documents && searchResults.documents.length > 0) {
        context += '=== DOCUMENT RESULTS ===\n';
        searchResults.documents.forEach((item, i) => {
          context += `${i + 1}. ${item.filename}\n`;
          context += `Content: ${item.content.substring(0, 300)}...\n\n`;
        });

        // Send documents as downloadable links
        try {
          await this.sendDocumentDownloads(ctx, searchResults.documents, detectedModel);
        } catch (docError) {
          console.error('Error sending document downloads:', docError);
        }
      }
      
      // Generate response with shorter timeout
      const conversationHistory = await chatService.getRecentMessages(ctx.session.id, 3);
      const messages = [
        ...conversationHistory.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.message
        })),
        { role: 'user', content: userMessage }
      ];
      
      const systemPrompt = `${openai.getSystemPrompt()}

===== CONTEXT =====
${context}
===== END CONTEXT =====

User Intent: ${intent.intent}
Urgency: ${intent.urgency}
Provide helpful, actionable advice based on the context above.`;
      
      let response;
      try {
        response = await Promise.race([
          openai.generateResponse(messages, systemPrompt),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Response timeout')), 12000)
          )
        ]);
      } catch (responseError) {
        console.error('❌ Response generation failed:', responseError.message);
        response = this.getFallbackResponse(intent, userMessage);
      }
      
      // Save response and send
      await chatService.saveMessage(ctx.session.id, response, 'assistant');
      
      // Format and send response
      const session = await conversationMemory.getOrCreateSession(userId, 'telegram');
      const formattedResponse = responseFormatter.formatTelegramResponse(
        response, 
        intent.intent === 'troubleshooting' ? 'troubleshooting' : 'general', 
        session.session_data
      );
      
      return await this.ux.sendChunkedResponse(ctx, formattedResponse.text);
      
    } catch (error) {
      console.error('Error in async processing:', error);
      const fallbackResponse = this.getFallbackResponse(intent, userMessage);
      return ctx.reply(fallbackResponse);
    }
  }

  // Helper methods for new conversation flow
  async handleBrowseModels(ctx) {
    try {
      const { data: products, error } = await knowledgeBase.supabase
        .from('products')
        .select('model_number, brand, product_line, display_size, operating_system, target_market')
        .eq('status', 'active')
        .order('product_line', { ascending: true })
        .order('display_size', { ascending: true });

      if (error) throw error;

      // Group by product line
      const productLines = {};
      products.forEach(product => {
        const line = product.product_line || 'Other';
        if (!productLines[line]) productLines[line] = [];
        productLines[line].push(product);
      });

      let response = '📚 **Samsung Interactive Display Models**\n\n';

      Object.keys(productLines).sort().forEach(line => {
        response += `**${line} Series**\n`;
        productLines[line].forEach(product => {
          const os = product.operating_system === 'Android' ? '📱' : '💻';
          response += `${os} ${product.model_number} (${product.display_size}) - ${product.target_market}\n`;
        });
        response += '\n';
      });

      response += '💡 **Tap any model number above or type it to get detailed specs!**';

      return ctx.reply(response);
    } catch (error) {
      console.error('Error browsing models:', error);
      return ctx.reply('Sorry, I encountered an error loading the model list. Please try again.');
    }
  }

  async handleAndroidSeriesHelp(ctx) {
    const androidKeyboard = [
      [{ text: 'WA65C (65")', callback_data: 'model_WA65C' }, { text: 'WA75C (75")', callback_data: 'model_WA75C' }],
      [{ text: 'WA65D (65")', callback_data: 'model_WA65D' }, { text: 'WA75D (75")', callback_data: 'model_WA75D' }],
      [{ text: 'WA65F (65")', callback_data: 'model_WA65F' }, { text: 'WA75F (75")', callback_data: 'model_WA75F' }],
      [{ text: '📱 All Android models', callback_data: 'all_android' }]
    ];

    return ctx.reply(`📱 **Android Display Series (WA)**

These displays run Android and support Google Play Store apps:

• **WA-C Series**: Standard Android displays
• **WA-D Series**: Enhanced Android with Google EDLA
• **WA-F Series**: Latest Android displays

Which model do you have?`, {
      reply_markup: { inline_keyboard: androidKeyboard }
    });
  }

  async handleTizenSeriesHelp(ctx) {
    const tizenKeyboard = [
      [{ text: 'WM55B (55")', callback_data: 'model_WM55B' }, { text: 'WM65B (65")', callback_data: 'model_WM65B' }],
      [{ text: 'WM75B (75")', callback_data: 'model_WM75B' }, { text: 'WM85B (85")', callback_data: 'model_WM85B' }],
      [{ text: '💻 All Tizen models', callback_data: 'all_tizen' }]
    ];

    return ctx.reply(`💻 **Tizen Display Series (WM)**

These displays run Samsung's Tizen OS and are designed for business use:

• **WM-B Series**: Digital flipcharts with touch
• Built-in whiteboard applications
• Corporate meeting room solutions

Which model do you have?`, {
      reply_markup: { inline_keyboard: tizenKeyboard }
    });
  }

  async handleBrowseManuals(ctx) {
    try {
      // Get all documents directly from database (more reliable than hybrid search)
      const { data: allDocuments, error } = await knowledgeBase.supabase
        .from('documents')
        .select('*')
        .order('original_name');

      if (error) throw error;

      if (!allDocuments || allDocuments.length === 0) {
        return ctx.reply('📚 No manuals are currently available in the system. Please contact support for assistance.');
      }

      // Filter for actual manuals and guides (more inclusive filter)
      const manuals = allDocuments.filter(doc => {
        const name = (doc.original_name || doc.filename || '').toLowerCase();
        return name.includes('manual') ||
               name.includes('guide') ||
               name.includes('instruction') ||
               name.includes('setup') ||
               name.includes('web') ||  // Many Samsung manuals have "WEB" in the name
               doc.file_type === 'application/pdf'; // Include all PDFs as they're likely manuals
      });

      if (manuals.length === 0) {
        return ctx.reply('📚 No user manuals found. The available documents may be technical specifications or other types.');
      }

      // Group by product series if possible
      const groupedManuals = {};
      manuals.forEach(manual => {
        const name = manual.original_name || manual.filename;
        const series = this.extractSeriesFromFilename(name);
        if (!groupedManuals[series]) groupedManuals[series] = [];
        groupedManuals[series].push(manual);
      });

      let response = '📚 **Available User Manuals & Guides**\n\n';

      Object.keys(groupedManuals).sort().forEach(series => {
        response += `**${series} Series:**\n`;
        groupedManuals[series].slice(0, 5).forEach(manual => { // Limit to 5 per series
          const downloadUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/files/${manual.id}`;
          const name = (manual.original_name || manual.filename).substring(0, 50);
          response += `📖 [${name}](${downloadUrl})\n`;
        });
        response += '\n';
      });

      response += '💡 **Tip**: Click any link above to download the manual directly!';

      return ctx.reply(response, {
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      });

    } catch (error) {
      console.error('Error browsing manuals:', error);
      return ctx.reply('📚 I encountered an error loading the manual list. Please try again or contact support.');
    }
  }

  extractSeriesFromFilename(filename) {
    // Try to extract product series from filename
    const match = filename.match(/([A-Z]{2})\d+[A-Z]?/i);
    if (match) {
      return match[1].toUpperCase();
    }

    // Fallback categorization
    if (filename.toLowerCase().includes('android')) return 'Android';
    if (filename.toLowerCase().includes('tizen')) return 'Tizen';
    if (filename.toLowerCase().includes('setup')) return 'Setup';
    if (filename.toLowerCase().includes('troubleshoot')) return 'Troubleshooting';

    return 'General';
  }

  // Helper methods for enhanced conversation flow
  isModelOnlyQuery(message) {
    const lowerMessage = message.toLowerCase().trim();
    // Check if message is just a model number or asking for basic specs
    return /^[a-z]{2}\d{2}[a-z]?$/i.test(lowerMessage) ||
           lowerMessage.includes('specs') ||
           lowerMessage.includes('specification') ||
           lowerMessage.includes('info') ||
           lowerMessage.includes('details');
  }

  needsModelInfo(message) {
    const lowerMessage = message.toLowerCase();
    const needsModelKeywords = [
      'my display', 'my screen', 'this display', 'this screen',
      'ports', 'connectivity', 'hdmi', 'ops', 'vga',
      'apps', 'play store', 'android', 'tizen',
      'specs', 'specifications', 'resolution',
      'not working', 'problem', 'issue', 'troubleshoot'
    ];

    return needsModelKeywords.some(keyword => lowerMessage.includes(keyword)) &&
           !this.hasModelReference(message);
  }

  hasModelReference(message) {
    // Check if message contains any model reference
    return /[a-z]{2}\d{2}[a-z]?/i.test(message);
  }

  isManualRequest(message) {
    const lowerMessage = message.toLowerCase();
    const manualKeywords = [
      'manual', 'user manual', 'instruction', 'guide', 'documentation',
      'download', 'pdf', 'document', 'setup guide', 'installation guide',
      'user guide', 'reference', 'handbook', 'datasheet'
    ];

    return manualKeywords.some(keyword => lowerMessage.includes(keyword));
  }

  async handleManualRequest(ctx, userMessage, detectedModel) {
    try {
      // Start typing indicator
      await this.ux.sendTyping(ctx, 3000);

      // Direct Supabase search for documents (more reliable than hybrid search)
      const { data: allDocuments, error } = await knowledgeBase.supabase
        .from('documents')
        .select('*')
        .order('original_name');

      if (error) {
        console.error('Error fetching documents:', error);
        throw error;
      }

      if (allDocuments && allDocuments.length > 0) {
        // Filter documents based on the request
        let relevantDocs = allDocuments;

        // If a specific model is detected, prioritize documents for that model
        if (detectedModel) {
          const modelSpecific = allDocuments.filter(doc => {
            const name = (doc.original_name || doc.filename || '').toLowerCase();
            return name.includes(detectedModel.toLowerCase());
          });

          if (modelSpecific.length > 0) {
            relevantDocs = modelSpecific;
          }
        }

        // Filter for manual-type documents
        const manuals = relevantDocs.filter(doc => {
          const name = (doc.original_name || doc.filename || '').toLowerCase();
          return name.includes('manual') ||
                 name.includes('guide') ||
                 name.includes('instruction') ||
                 name.includes('web') ||  // Many manuals have "WEB" in the name
                 name.includes('pdf') ||  // Include all PDFs as they're likely manuals
                 doc.file_type === 'application/pdf'; // Include all PDF files
        });

        if (manuals.length > 0) {
          await this.sendDocumentDownloads(ctx, manuals, detectedModel);

          let response = `📚 I found ${manuals.length} manual(s) for you!`;

          if (detectedModel) {
            const modelSpecificCount = manuals.filter(doc =>
              (doc.original_name || '').toLowerCase().includes(detectedModel.toLowerCase())
            ).length;

            if (modelSpecificCount > 0) {
              response += ` ${modelSpecificCount} are specifically for the ${detectedModel}.`;
            }
          }

          response += '\n\n💡 **Need something specific?** Try asking:\n';
          response += '• "Setup guide for [model]"\n';
          response += '• "Troubleshooting manual"\n';
          response += '• "Installation instructions"';

          return ctx.reply(response);
        } else {
          // No manuals found after filtering, show all documents
          await this.sendDocumentDownloads(ctx, allDocuments.slice(0, 5), detectedModel);
          return ctx.reply(`📚 I found ${allDocuments.length} document(s) that might help you!`);
        }
        // No documents found - offer alternatives
        let response = '📚 I don\'t have specific documents matching your request';

        if (detectedModel) {
          response += ` for the ${detectedModel}`;
        }

        response += '.\n\n🔍 **Let me help you find what you need:**\n';

        const helpKeyboard = [
          [{ text: '📱 Browse all manuals', callback_data: 'browse_manuals' }],
          [{ text: '🔍 Search by model', callback_data: 'search_by_model' }],
          [{ text: '📞 Contact support', callback_data: 'contact_support' }]
        ];

        if (!detectedModel) {
          response += '\n💡 **Tip**: Tell me your model number (like WA65C) for specific manuals!';
        }

        return ctx.reply(response, {
          reply_markup: { inline_keyboard: helpKeyboard }
        });
      }

    } catch (error) {
      console.error('Error handling manual request:', error);
      return ctx.reply('📚 I encountered an error searching for documents. Please try again or contact support for manual downloads.');
    }
  }

  async askForModel(ctx, originalMessage) {
    const modelKeyboard = [
      [{ text: '📱 Android (WA series)', callback_data: 'android_series' }],
      [{ text: '💻 Tizen (WM series)', callback_data: 'tizen_series' }],
      [{ text: '🔍 I know my model', callback_data: 'know_model' }],
      [{ text: '📷 Photo of label', callback_data: 'photo_help' }]
    ];

    const askMessage = `To help you with "${originalMessage}", I need to know which Samsung display model you're using.

💡 **Quick identification**:
• **WA series** (WA65C, WA75D, etc.) = Android displays
• **WM series** (WM55B, WM75B, etc.) = Tizen displays

Which type do you have?`;

    return ctx.reply(askMessage, {
      reply_markup: { inline_keyboard: modelKeyboard }
    });
  }

  async handleModelQuery(ctx, productInfo, originalMessage) {
    // Generate quick spec response
    const quickSpecs = this.generateQuickSpecs(productInfo);

    const specKeyboard = [
      [{ text: '🔌 Connectivity', callback_data: `specs_connectivity_${productInfo.model_number}` }],
      [{ text: '📱 Software', callback_data: `specs_software_${productInfo.model_number}` }],
      [{ text: '📏 Physical', callback_data: `specs_physical_${productInfo.model_number}` }],
      [{ text: '🛠️ Setup Guide', callback_data: `setup_${productInfo.model_number}` }]
    ];

    return ctx.reply(quickSpecs, {
      reply_markup: { inline_keyboard: specKeyboard },
      parse_mode: 'Markdown'
    });
  }

  generateQuickSpecs(productInfo) {
    let specs = `📱 **${productInfo.model_number}** - ${productInfo.brand} ${productInfo.product_line}\n\n`;

    // Basic info
    specs += `🖥️ **Display**: ${productInfo.display_size} ${productInfo.resolution || 'UHD'}\n`;
    specs += `💻 **OS**: ${productInfo.operating_system}\n`;

    // Key connectivity (using new columns!)
    if (productInfo.ops_support !== null) {
      specs += `🔌 **OPS Support**: ${productInfo.ops_support ? 'Yes' : 'No'}\n`;
    }
    if (productInfo.hdmi_ports) {
      specs += `📺 **HDMI Ports**: ${productInfo.hdmi_ports}\n`;
    }

    // Key software features
    if (productInfo.google_play_store !== null) {
      specs += `📱 **Google Play Store**: ${productInfo.google_play_store ? 'Yes' : 'No'}\n`;
    }
    if (productInfo.browser_support !== null) {
      specs += `🌐 **Browser**: ${productInfo.browser_support ? 'Built-in' : 'No'}\n`;
    }

    // Touch and target
    if (productInfo.touch_points) {
      specs += `👆 **Touch**: ${productInfo.touch_points}-point\n`;
    }
    if (productInfo.target_market) {
      specs += `🎯 **Target**: ${productInfo.target_market}\n`;
    }

    specs += `\n💡 **Tap buttons below for detailed specs or ask me anything!**`;

    return specs;
  }

  async handleSpecsCallback(ctx, callbackData) {
    const [_, category, modelNumber] = callbackData.split('_');

    try {
      const productInfo = await productService.getProductByModel(modelNumber);
      if (!productInfo) {
        return ctx.reply('Sorry, I couldn\'t find information for that model.');
      }

      let response = '';

      switch (category) {
        case 'connectivity':
          response = this.generateConnectivitySpecs(productInfo);
          break;
        case 'software':
          response = this.generateSoftwareSpecs(productInfo);
          break;
        case 'physical':
          response = this.generatePhysicalSpecs(productInfo);
          break;
        default:
          response = 'Specification category not found.';
      }

      return ctx.reply(response, { parse_mode: 'Markdown' });
    } catch (error) {
      console.error('Error handling specs callback:', error);
      return ctx.reply('Sorry, I encountered an error retrieving the specifications.');
    }
  }

  async handleModelCallback(ctx, callbackData) {
    const modelNumber = callbackData.replace('model_', '');

    try {
      const productInfo = await productService.getProductByModel(modelNumber);
      if (productInfo) {
        return this.handleModelQuery(ctx, productInfo, `Tell me about ${modelNumber}`);
      } else {
        return ctx.reply(`Sorry, I couldn't find information for model ${modelNumber}. Please check the model number and try again.`);
      }
    } catch (error) {
      console.error('Error handling model callback:', error);
      return ctx.reply('Sorry, I encountered an error retrieving the model information.');
    }
  }

  async handleSizeCallback(ctx, callbackData) {
    const size = callbackData.replace('size_', '');

    if (size === 'other') {
      return ctx.reply('Please tell me the size of your display in inches (like 43", 55", 65", etc.) and I\'ll help you identify the model.');
    }

    try {
      const { data: products, error } = await knowledgeBase.supabase
        .from('products')
        .select('*')
        .eq('status', 'active')
        .like('display_size', `${size}%`)
        .order('model_number');

      if (error) throw error;

      if (products.length === 0) {
        return ctx.reply(`I don't have any ${size}" models in my database. Please check the size or try browsing all models.`);
      }

      let response = `📏 **${size}" Samsung Interactive Displays**\n\n`;

      products.forEach(product => {
        const os = product.operating_system === 'Android' ? '📱' : '💻';
        response += `${os} **${product.model_number}** - ${product.product_line}\n`;
        response += `   ${product.target_market} • ${product.operating_system}\n\n`;
      });

      response += '💡 **Tap any model number above or type it for detailed specs!**';

      return ctx.reply(response, { parse_mode: 'Markdown' });
    } catch (error) {
      console.error('Error handling size callback:', error);
      return ctx.reply('Sorry, I encountered an error finding models by size.');
    }
  }

  generateConnectivitySpecs(productInfo) {
    let specs = `🔌 **${productInfo.model_number} Connectivity**\n\n`;

    // Ports using new columns
    specs += `**Ports & Connections:**\n`;
    if (productInfo.hdmi_ports) specs += `• HDMI: ${productInfo.hdmi_ports} ports\n`;
    if (productInfo.vga_ports) specs += `• VGA: ${productInfo.vga_ports} ports\n`;
    if (productInfo.usb_c_ports) specs += `• USB-C: ${productInfo.usb_c_ports} ports\n`;
    if (productInfo.ops_support !== null) {
      specs += `• OPS Slot: ${productInfo.ops_support ? 'Yes' : 'No'}\n`;
    }

    // Wireless
    specs += `\n**Wireless:**\n`;
    if (productInfo.wifi_support !== null) {
      specs += `• WiFi: ${productInfo.wifi_support ? 'Yes' : 'No'}\n`;
    }
    if (productInfo.bluetooth_support !== null) {
      specs += `• Bluetooth: ${productInfo.bluetooth_support ? 'Yes' : 'No'}\n`;
    }

    // Additional connectivity from JSONB if available
    if (productInfo.connectivity) {
      const conn = productInfo.connectivity;
      if (conn.ethernet) specs += `• Ethernet: Yes\n`;
      if (conn.audio_out) specs += `• Audio Out: Yes\n`;
      if (conn.rs232) specs += `• RS232: Yes\n`;
    }

    return specs;
  }

  generateSoftwareSpecs(productInfo) {
    let specs = `📱 **${productInfo.model_number} Software**\n\n`;

    specs += `**Operating System:**\n`;
    specs += `• OS: ${productInfo.operating_system}\n`;
    if (productInfo.os_version) specs += `• Version: ${productInfo.os_version}\n`;

    specs += `\n**Apps & Features:**\n`;
    if (productInfo.browser_support !== null) {
      specs += `• Browser: ${productInfo.browser_support ? 'Built-in' : 'No'}\n`;
    }
    if (productInfo.google_play_store !== null) {
      specs += `• Google Play Store: ${productInfo.google_play_store ? 'Yes' : 'No'}\n`;
    }
    if (productInfo.google_edla_certified !== null) {
      specs += `• Google EDLA: ${productInfo.google_edla_certified ? 'Certified' : 'No'}\n`;
    }
    if (productInfo.app_installation_method) {
      const methodMap = {
        'none': 'No app installation',
        'sideloading': 'Sideloading only',
        'play_store': 'Google Play Store',
        'both': 'Play Store + Sideloading'
      };
      specs += `• App Installation: ${methodMap[productInfo.app_installation_method]}\n`;
    }

    specs += `\n**Media Features:**\n`;
    if (productInfo.screen_casting_support !== null) {
      specs += `• Screen Casting: ${productInfo.screen_casting_support ? 'Yes' : 'No'}\n`;
    }
    if (productInfo.screen_recording_support !== null) {
      specs += `• Screen Recording: ${productInfo.screen_recording_support ? 'Yes' : 'No'}\n`;
    }
    if (productInfo.whiteboard_application) {
      specs += `• Whiteboard App: ${productInfo.whiteboard_application}\n`;
    }

    return specs;
  }

  async sendDocumentDownloads(ctx, documents, detectedModel = null) {
    if (!documents || documents.length === 0) return;

    try {
      console.log(`📚 sendDocumentDownloads: Processing ${documents.length} documents`);

      // Filter and prioritize documents
      const relevantDocs = this.filterRelevantDocuments(documents, detectedModel);

      console.log(`📚 After filtering: ${relevantDocs.length} relevant documents`);

      if (relevantDocs.length === 0) {
        console.log('📚 No relevant documents found after filtering');
        // Send all documents if filtering is too strict
        const allDocs = documents.slice(0, 3); // Limit to 3 for safety
        if (allDocs.length > 0) {
          console.log(`📚 Sending all ${allDocs.length} documents as fallback`);
          return this.sendDocumentDownloadsSimple(ctx, allDocs);
        }
        return;
      }

      // Create download message
      let downloadMessage = '📚 **Related Documents & Manuals**\n\n';

      relevantDocs.forEach((doc, index) => {
        const docType = this.getDocumentType(doc.filename || doc.original_name);
        const emoji = this.getDocumentEmoji(docType);
        const downloadUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/files/${doc.id}`;

        // Escape Markdown characters in filename
        const safeName = this.escapeMarkdown(doc.original_name || doc.filename);
        downloadMessage += `${emoji} **${safeName}**\n`;
        downloadMessage += `📥 [Download](${downloadUrl})\n`;

        if (doc.content && doc.content.length > 50) {
          // Escape Markdown characters in content preview
          const safeContent = this.escapeMarkdown(doc.content.substring(0, 100));
          downloadMessage += `💡 ${safeContent}...\n`;
        }
        downloadMessage += '\n';
      });

      downloadMessage += '💡 **Tip**: Click the download links above to get the full documents!';

      // Check if we have a proper external URL for inline buttons
      const baseUrl = process.env.BASE_URL;
      const hasValidUrl = baseUrl && !baseUrl.includes('localhost') && !baseUrl.includes('127.0.0.1');

      if (hasValidUrl) {
        // Send with inline keyboard for quick actions (only if we have a valid external URL)
        const downloadKeyboard = relevantDocs.slice(0, 3).map(doc => [{
          text: `📥 ${(doc.original_name || doc.filename).substring(0, 30).replace(/[_*[\]()~`>#+=|{}.!-]/g, '')}...`,
          url: `${baseUrl}/files/${doc.id}`
        }]);

        return ctx.reply(downloadMessage, {
          parse_mode: 'Markdown',
          reply_markup: {
            inline_keyboard: downloadKeyboard
          },
          disable_web_page_preview: true
        });
      } else {
        // Send without inline keyboard (for localhost/development)
        return ctx.reply(downloadMessage, {
          parse_mode: 'Markdown',
          disable_web_page_preview: true
        });
      }

    } catch (error) {
      console.error('Error sending document downloads:', error);
      // Fallback to simple text message
      const docNames = documents.map(d => d.original_name || d.filename).join(', ');
      return ctx.reply(`📚 Found relevant documents: ${docNames}. Please contact support for download links.`);
    }
  }

  filterRelevantDocuments(documents, detectedModel) {
    // Sort by relevance and limit to most useful documents
    let filtered = documents.slice(0, 5); // Limit to 5 documents max

    if (detectedModel) {
      // Prioritize documents that mention the specific model
      filtered = filtered.sort((a, b) => {
        const aRelevant = (a.filename || a.original_name || '').toLowerCase().includes(detectedModel.toLowerCase()) ||
                         (a.content || '').toLowerCase().includes(detectedModel.toLowerCase());
        const bRelevant = (b.filename || b.original_name || '').toLowerCase().includes(detectedModel.toLowerCase()) ||
                         (b.content || '').toLowerCase().includes(detectedModel.toLowerCase());

        if (aRelevant && !bRelevant) return -1;
        if (!aRelevant && bRelevant) return 1;
        return 0;
      });
    }

    // Filter out very short or irrelevant documents
    return filtered.filter(doc => {
      const name = (doc.original_name || doc.filename || '').toLowerCase();
      const content = (doc.content || '');

      // Include if it's a manual, guide, or has substantial content
      // Made more lenient - include if name suggests it's a document OR has some content
      return name.includes('manual') ||
             name.includes('guide') ||
             name.includes('instruction') ||
             name.includes('setup') ||
             name.includes('pdf') ||
             name.includes('doc') ||
             content.length > 50; // Reduced from 200 to 50
    });
  }

  getDocumentType(filename) {
    const name = filename.toLowerCase();
    if (name.includes('manual')) return 'manual';
    if (name.includes('guide')) return 'guide';
    if (name.includes('setup') || name.includes('install')) return 'setup';
    if (name.includes('troubleshoot') || name.includes('problem')) return 'troubleshoot';
    if (name.includes('spec') || name.includes('datasheet')) return 'specs';
    return 'document';
  }

  getDocumentEmoji(docType) {
    const emojis = {
      manual: '📖',
      guide: '📋',
      setup: '🔧',
      troubleshoot: '🛠️',
      specs: '📊',
      document: '📄'
    };
    return emojis[docType] || '📄';
  }

  escapeMarkdown(text) {
    if (!text) return '';
    // Escape Markdown special characters
    return text.replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
  }

  async sendDocumentDownloadsSimple(ctx, documents) {
    try {
      let downloadMessage = '📚 **Available Documents**\n\n';

      documents.forEach((doc, index) => {
        const downloadUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/files/${doc.id}`;
        const safeName = this.escapeMarkdown(doc.original_name || doc.filename);
        downloadMessage += `📄 **${safeName}**\n`;
        downloadMessage += `📥 [Download](${downloadUrl})\n\n`;
      });

      downloadMessage += '💡 **Tip**: Click the download links above to get the documents!';

      return ctx.reply(downloadMessage, {
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      });

    } catch (error) {
      console.error('Error sending simple document downloads:', error);
      const docNames = documents.map(d => d.original_name || d.filename).join(', ');
      return ctx.reply(`📚 Found documents: ${docNames}. Please contact support for download links.`);
    }
  }

  generatePhysicalSpecs(productInfo) {
    let specs = `📏 **${productInfo.model_number} Physical Specifications**\n\n`;

    specs += `**Display:**\n`;
    specs += `• Size: ${productInfo.display_size}\n`;
    if (productInfo.resolution) specs += `• Resolution: ${productInfo.resolution}\n`;
    if (productInfo.aspect_ratio) specs += `• Aspect Ratio: ${productInfo.aspect_ratio}\n`;
    if (productInfo.brightness_nits) specs += `• Brightness: ${productInfo.brightness_nits} nits\n`;

    if (productInfo.touch_points) {
      specs += `\n**Touch:**\n`;
      specs += `• Touch Points: ${productInfo.touch_points}\n`;
      if (productInfo.touch_technology) specs += `• Technology: ${productInfo.touch_technology}\n`;
    }

    if (productInfo.dimensions || productInfo.weight_kg) {
      specs += `\n**Physical:**\n`;
      if (productInfo.dimensions) {
        if (typeof productInfo.dimensions === 'object') {
          specs += `• Dimensions: ${productInfo.dimensions.width}×${productInfo.dimensions.height}×${productInfo.dimensions.depth}mm\n`;
        } else {
          specs += `• Dimensions: ${productInfo.dimensions}\n`;
        }
      }
      if (productInfo.weight_kg) specs += `• Weight: ${productInfo.weight_kg}kg\n`;
      if (productInfo.vesa_mount) specs += `• VESA Mount: ${productInfo.vesa_mount}\n`;
    }

    if (productInfo.power_consumption_watts) {
      specs += `\n**Power:**\n`;
      specs += `• Power Consumption: ${productInfo.power_consumption_watts}W\n`;
      if (productInfo.power_supply) specs += `• Power Supply: ${productInfo.power_supply}\n`;
    }

    return specs;
  }

  // Helper method for fallback responses
  getFallbackResponse(intent, userMessage) {
    if (intent.intent === 'troubleshooting') {
      return '🔧 I\'m having trouble accessing the knowledge base right now. For immediate help:\n\n• Try restarting your device\n• Check all cable connections\n• Use /help for available commands\n• Contact <NAME_EMAIL>';
    } else if (intent.intent === 'product_info') {
      return '📱 I\'m having trouble accessing product information right now. Please:\n\n• Use /devices to see your registered devices\n• Try asking about specific model numbers\n• Use /help for available commands';
    } else {
      return '🤖 I\'m experiencing some technical difficulties right now. Please try:\n\n• Using specific commands like /help or /categories\n• Asking simpler, more direct questions\n• Trying again in a moment';
    }
  }

  async setWebhook(url) {
    try {
      // Add timeout and retry logic for webhook setting
      const setWebhookWithTimeout = Promise.race([
        this.bot.telegram.setWebhook(url),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Webhook setup timeout')), 15000)
        )
      ]);
      
      await setWebhookWithTimeout;
      console.log('Webhook set successfully');
    } catch (error) {
      console.error('Error setting webhook:', error);
      
      // If it's a network timeout, try to continue anyway
      if (error.message.includes('timeout') || error.message.includes('network')) {
        console.log('Webhook setup timed out, but continuing...');
        return; // Don't throw, just continue
      }
      
      throw error;
    }
  }

  async removeWebhook() {
    try {
      await this.bot.telegram.deleteWebhook();
      console.log('Webhook removed successfully');
    } catch (error) {
      console.error('Error removing webhook:', error);
      throw error;
    }
  }

  getWebhookMiddleware() {
    return this.bot.webhookCallback('/webhook');
  }

  async startPolling() {
    try {
      // Add timeout for bot launch
      const launchWithTimeout = Promise.race([
        this.bot.launch(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Bot launch timeout')), 30000)
        )
      ]);
      
      await launchWithTimeout;
      console.log('Telegram bot started in polling mode');
    } catch (error) {
      console.error('Error starting bot in polling mode:', error);
      
      // If it's a network timeout, log but don't crash
      if (error.message.includes('timeout') || error.message.includes('network')) {
        console.log('Bot launch timed out, but server will continue running...');
        return;
      }
      
      throw error;
    }
  }

  async stop() {
    try {
      this.bot.stop();
      console.log('Telegram bot stopped');
    } catch (error) {
      console.error('Error stopping bot:', error);
      throw error;
    }
  }
}

module.exports = new TelegramBotService();