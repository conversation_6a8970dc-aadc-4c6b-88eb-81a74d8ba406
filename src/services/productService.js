const database = require('../config/database');

class ProductService {
  constructor() {
    this.supabase = database.getClient();
  }

  /**
   * Find products by model number or partial match
   */
  async findProducts(query, options = {}) {
    try {
      const { limit = 10, includeFeatures = true } = options;
      
      let queryBuilder = this.supabase
        .from('products')
        .select(`
          *,
          ${includeFeatures ? `
            product_features (
              feature_category,
              feature_name, 
              feature_value,
              is_key_feature
            )
          ` : ''}
        `)
        .eq('status', 'active')
        .limit(limit);

      // Smart search - try exact match first, then partial
      if (query) {
        const cleanQuery = query.trim().toUpperCase();
        
        // Try exact model number match first
        queryBuilder = queryBuilder.or(`
          model_number.ilike.${cleanQuery},
          model_number.ilike.%${cleanQuery}%,
          brand.ilike.%${cleanQuery}%,
          product_line.ilike.%${cleanQuery}%
        `);
      }

      const { data, error } = await queryBuilder;
      
      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error finding products:', error);
      return [];
    }
  }

  /**
   * Get product by exact model number
   */
  async getProductByModel(modelNumber) {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .select(`
          *,
          product_features (
            feature_category,
            feature_name,
            feature_value,
            feature_unit,
            is_key_feature
          )
        `)
        .eq('model_number', modelNumber.toUpperCase())
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      return data;
    } catch (error) {
      console.error('Error getting product by model:', error);
      return null;
    }
  }

  /**
   * Search products using the materialized view
   */
  async searchProducts(searchText, filters = {}) {
    try {
      const { 
        brand,
        productType,
        displaySize,
        resolution,
        limit = 10 
      } = filters;

      let query = this.supabase
        .from('product_search_index')
        .select('*')
        .limit(limit);

      // Text search using PostgreSQL full-text search
      if (searchText) {
        query = query.textSearch('search_text', searchText, {
          type: 'websearch',
          config: 'english'
        });
      }

      // Apply filters
      if (brand) {
        query = query.eq('brand', brand);
      }
      if (productType) {
        query = query.eq('product_type', productType);
      }
      if (displaySize) {
        query = query.eq('display_size', displaySize);
      }
      if (resolution) {
        query = query.eq('resolution', resolution);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  }

  /**
   * Get all unique values for filtering
   */
  async getFilterOptions() {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .select('brand, product_type, display_size, resolution')
        .eq('status', 'active');

      if (error) throw error;

      const options = {
        brands: [...new Set(data.map(p => p.brand))].filter(Boolean),
        productTypes: [...new Set(data.map(p => p.product_type))].filter(Boolean),
        displaySizes: [...new Set(data.map(p => p.display_size))].filter(Boolean),
        resolutions: [...new Set(data.map(p => p.resolution))].filter(Boolean)
      };

      return options;
    } catch (error) {
      console.error('Error getting filter options:', error);
      return { brands: [], productTypes: [], displaySizes: [], resolutions: [] };
    }
  }

  /**
   * Get product specifications formatted for display
   */
  async getProductSpecs(modelNumber) {
    try {
      const product = await this.getProductByModel(modelNumber);
      if (!product) return null;

      // Group features by category
      const featuresByCategory = {};
      if (product.product_features) {
        product.product_features.forEach(feature => {
          if (!featuresByCategory[feature.feature_category]) {
            featuresByCategory[feature.feature_category] = [];
          }
          featuresByCategory[feature.feature_category].push(feature);
        });
      }

      return {
        product,
        featuresByCategory,
        keyFeatures: product.product_features?.filter(f => f.is_key_feature) || []
      };
    } catch (error) {
      console.error('Error getting product specs:', error);
      return null;
    }
  }

  /**
   * Extract product model from user message
   */
  extractProductModel(message) {
    // Common product model patterns
    const patterns = [
      /\b(WA\d+[A-Z]*)\b/i,      // WA65F, WA75F, etc
      /\b(WAD\d+[A-Z]*)\b/i,     // WAD65, etc
      /\b(WM\d+[A-Z]*)\b/i,      // WM55B, WM65B, etc
      /\b(CVTE[- ]?IFP[- ]?\d+)\b/i, // CVTE-IFP-65, CVTE IFP 75
      /\b([A-Z]{2,4}\d+[A-Z]*)\b/i // General pattern for model numbers
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) {
        const extracted = match[1].replace(/[- ]/g, '').toUpperCase();
        // Avoid matching generic words like "SAMSUNG", "DISPLAYS", etc.
        if (extracted.length >= 4 && /\d/.test(extracted)) {
          return extracted;
        }
      }
    }

    return null;
  }

  /**
   * Get similar products
   */
  async getSimilarProducts(modelNumber, limit = 3) {
    try {
      const product = await this.getProductByModel(modelNumber);
      if (!product) return [];

      const { data, error } = await this.supabase
        .from('products')
        .select('model_number, brand, product_line, display_size, resolution')
        .eq('status', 'active')
        .neq('model_number', modelNumber)
        .or(`
          brand.eq.${product.brand},
          product_line.eq.${product.product_line},
          display_size.eq.${product.display_size}
        `)
        .limit(limit);

      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error getting similar products:', error);
      return [];
    }
  }

  /**
   * Add or update product information
   */
  async upsertProduct(productData) {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .upsert(productData, { onConflict: 'model_number' })
        .select()
        .single();

      if (error) throw error;

      // Refresh materialized view
      await this.refreshProductIndex();
      
      return data;
    } catch (error) {
      console.error('Error upserting product:', error);
      throw error;
    }
  }

  /**
   * Refresh the product search materialized view
   */
  async refreshProductIndex() {
    try {
      const { error } = await this.supabase.rpc('refresh_product_search_index');
      if (error) throw error;
      console.log('✅ Product search index refreshed');
    } catch (error) {
      console.error('Error refreshing product index:', error);
    }
  }

  /**
   * Format product information for chat responses
   */
  formatProductInfo(product, includeSpecs = false) {
    if (!product) return 'Product not found.';

    let info = `📱 **${product.model_number}** - ${product.brand} ${product.product_line}\n`;
    info += `📺 ${product.display_size} ${product.product_type}\n`;
    
    if (product.resolution) {
      info += `🔍 Resolution: ${product.resolution}\n`;
    }
    
    if (product.touch_points) {
      info += `👆 Touch: ${product.touch_points} points (${product.touch_technology})\n`;
    }

    if (product.operating_system) {
      info += `💻 OS: ${product.operating_system}\n`;
    }

    // Add connectivity information
    if (product.connectivity) {
      info += `\n🔌 **Connectivity:**\n`;
      const conn = product.connectivity;
      
      // Display ports
      const ports = [];
      if (conn.hdmi && conn.hdmi > 0) ports.push(`${conn.hdmi} HDMI port${conn.hdmi > 1 ? 's' : ''}`);
      if (conn.vga && conn.vga > 0) ports.push(`${conn.vga} VGA port${conn.vga > 1 ? 's' : ''}`);
      if (conn.usb_c && conn.usb_c > 0) ports.push(`${conn.usb_c} USB-C port${conn.usb_c > 1 ? 's' : ''}`);
      if (conn.ops === true) ports.push('OPS slot');
      
      if (ports.length > 0) {
        info += `• Ports: ${ports.join(', ')}\n`;
      }
      
      // Wireless connectivity
      const wireless = [];
      if (conn.wifi === true) wireless.push('WiFi');
      if (conn.bluetooth === true) wireless.push('Bluetooth');
      
      if (wireless.length > 0) {
        info += `• Wireless: ${wireless.join(', ')}\n`;
      }
    }

    if (includeSpecs && product.product_features) {
      const keyFeatures = product.product_features.filter(f => f.is_key_feature);
      if (keyFeatures.length > 0) {
        info += `\n⚡ **Key Features:**\n`;
        keyFeatures.forEach(feature => {
          info += `• ${feature.feature_name}: ${feature.feature_value}\n`;
        });
      }
    }

    return info;
  }
}

module.exports = new ProductService();