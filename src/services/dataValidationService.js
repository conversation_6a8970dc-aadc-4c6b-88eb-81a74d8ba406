/**
 * Data Validation Service
 * Ensures data integrity for critical specifications migration
 */

class DataValidationService {
  constructor(supabase) {
    this.supabase = supabase;
  }

  /**
   * Validate all migrated data consistency
   */
  async validateMigration() {
    console.log('🔍 Running comprehensive migration validation...\n');
    
    const results = {
      totalProducts: 0,
      validationErrors: [],
      warnings: [],
      summary: {}
    };

    try {
      // Get all active products
      const { data: products, error } = await this.supabase
        .from('products')
        .select('*')
        .eq('status', 'active');

      if (error) throw error;

      results.totalProducts = products.length;
      console.log(`📊 Validating ${products.length} products...\n`);

      // Run all validation checks
      await this.validateConnectivityData(products, results);
      await this.validateSpecificationData(products, results);
      await this.validateDataTypes(products, results);
      await this.validateBusinessLogic(products, results);
      await this.validateCompleteness(products, results);

      // Generate summary
      this.generateValidationSummary(results);

      return results;

    } catch (error) {
      console.error('❌ Validation failed:', error);
      throw error;
    }
  }

  /**
   * Validate connectivity data consistency
   */
  async validateConnectivityData(products, results) {
    console.log('🔌 Validating connectivity data...');
    
    let connectivityErrors = 0;
    
    products.forEach(product => {
      const model = product.model_number;
      const conn = product.connectivity || {};
      
      // Validate OPS support
      if (conn.ops !== undefined && product.ops_support !== null) {
        if (conn.ops !== product.ops_support) {
          results.validationErrors.push({
            model,
            field: 'ops_support',
            issue: 'JSONB/Column mismatch',
            jsonbValue: conn.ops,
            columnValue: product.ops_support
          });
          connectivityErrors++;
        }
      }
      
      // Validate HDMI ports
      if (conn.hdmi !== undefined && product.hdmi_ports !== null) {
        if (conn.hdmi !== product.hdmi_ports) {
          results.validationErrors.push({
            model,
            field: 'hdmi_ports',
            issue: 'JSONB/Column mismatch',
            jsonbValue: conn.hdmi,
            columnValue: product.hdmi_ports
          });
          connectivityErrors++;
        }
      }
      
      // Validate VGA ports
      if (conn.vga !== undefined && product.vga_ports !== null) {
        if (conn.vga !== product.vga_ports) {
          results.validationErrors.push({
            model,
            field: 'vga_ports',
            issue: 'JSONB/Column mismatch',
            jsonbValue: conn.vga,
            columnValue: product.vga_ports
          });
          connectivityErrors++;
        }
      }
      
      // Validate USB-C ports
      if (conn.usb_c !== undefined && product.usb_c_ports !== null) {
        if (conn.usb_c !== product.usb_c_ports) {
          results.validationErrors.push({
            model,
            field: 'usb_c_ports',
            issue: 'JSONB/Column mismatch',
            jsonbValue: conn.usb_c,
            columnValue: product.usb_c_ports
          });
          connectivityErrors++;
        }
      }
      
      // Validate WiFi support
      if (conn.wifi !== undefined && product.wifi_support !== null) {
        if (conn.wifi !== product.wifi_support) {
          results.validationErrors.push({
            model,
            field: 'wifi_support',
            issue: 'JSONB/Column mismatch',
            jsonbValue: conn.wifi,
            columnValue: product.wifi_support
          });
          connectivityErrors++;
        }
      }
      
      // Validate Bluetooth support
      if (conn.bluetooth !== undefined && product.bluetooth_support !== null) {
        if (conn.bluetooth !== product.bluetooth_support) {
          results.validationErrors.push({
            model,
            field: 'bluetooth_support',
            issue: 'JSONB/Column mismatch',
            jsonbValue: conn.bluetooth,
            columnValue: product.bluetooth_support
          });
          connectivityErrors++;
        }
      }
    });
    
    console.log(`   ${connectivityErrors === 0 ? '✅' : '❌'} Connectivity validation: ${connectivityErrors} errors`);
    results.summary.connectivityErrors = connectivityErrors;
  }

  /**
   * Validate specification data consistency
   */
  async validateSpecificationData(products, results) {
    console.log('📋 Validating specification data...');
    
    let specificationErrors = 0;
    
    products.forEach(product => {
      const model = product.model_number;
      const specs = product.specifications || {};
      
      // Validate browser support
      if (specs.browser !== undefined && product.browser_support !== null) {
        if (specs.browser !== product.browser_support) {
          results.validationErrors.push({
            model,
            field: 'browser_support',
            issue: 'JSONB/Column mismatch',
            jsonbValue: specs.browser,
            columnValue: product.browser_support
          });
          specificationErrors++;
        }
      }
      
      // Validate Google Play Store
      if (specs.google_play_store !== undefined && product.google_play_store !== null) {
        if (specs.google_play_store !== product.google_play_store) {
          results.validationErrors.push({
            model,
            field: 'google_play_store',
            issue: 'JSONB/Column mismatch',
            jsonbValue: specs.google_play_store,
            columnValue: product.google_play_store
          });
          specificationErrors++;
        }
      }
      
      // Validate Google EDLA
      if (specs.google_edla !== undefined && product.google_edla_certified !== null) {
        if (specs.google_edla !== product.google_edla_certified) {
          results.validationErrors.push({
            model,
            field: 'google_edla_certified',
            issue: 'JSONB/Column mismatch',
            jsonbValue: specs.google_edla,
            columnValue: product.google_edla_certified
          });
          specificationErrors++;
        }
      }
      
      // Validate screen casting
      if (specs.screen_casting !== undefined && product.screen_casting_support !== null) {
        if (specs.screen_casting !== product.screen_casting_support) {
          results.validationErrors.push({
            model,
            field: 'screen_casting_support',
            issue: 'JSONB/Column mismatch',
            jsonbValue: specs.screen_casting,
            columnValue: product.screen_casting_support
          });
          specificationErrors++;
        }
      }
      
      // Validate screen recording
      if (specs.screen_recording !== undefined && product.screen_recording_support !== null) {
        if (specs.screen_recording !== product.screen_recording_support) {
          results.validationErrors.push({
            model,
            field: 'screen_recording_support',
            issue: 'JSONB/Column mismatch',
            jsonbValue: specs.screen_recording,
            columnValue: product.screen_recording_support
          });
          specificationErrors++;
        }
      }
      
      // Validate warranty
      if (specs.warranty !== undefined && product.warranty_period !== null) {
        if (specs.warranty !== product.warranty_period) {
          results.validationErrors.push({
            model,
            field: 'warranty_period',
            issue: 'JSONB/Column mismatch',
            jsonbValue: specs.warranty,
            columnValue: product.warranty_period
          });
          specificationErrors++;
        }
      }
      
      // Validate whiteboard
      if (specs.whiteboard !== undefined && product.whiteboard_application !== null) {
        if (specs.whiteboard !== product.whiteboard_application) {
          results.validationErrors.push({
            model,
            field: 'whiteboard_application',
            issue: 'JSONB/Column mismatch',
            jsonbValue: specs.whiteboard,
            columnValue: product.whiteboard_application
          });
          specificationErrors++;
        }
      }
    });
    
    console.log(`   ${specificationErrors === 0 ? '✅' : '❌'} Specification validation: ${specificationErrors} errors`);
    results.summary.specificationErrors = specificationErrors;
  }

  /**
   * Validate data types and constraints
   */
  async validateDataTypes(products, results) {
    console.log('🔢 Validating data types and constraints...');
    
    let typeErrors = 0;
    
    products.forEach(product => {
      const model = product.model_number;
      
      // Validate port counts are within reasonable ranges
      if (product.hdmi_ports !== null && (product.hdmi_ports < 0 || product.hdmi_ports > 10)) {
        results.validationErrors.push({
          model,
          field: 'hdmi_ports',
          issue: 'Value out of range',
          value: product.hdmi_ports,
          expectedRange: '0-10'
        });
        typeErrors++;
      }
      
      if (product.vga_ports !== null && (product.vga_ports < 0 || product.vga_ports > 5)) {
        results.validationErrors.push({
          model,
          field: 'vga_ports',
          issue: 'Value out of range',
          value: product.vga_ports,
          expectedRange: '0-5'
        });
        typeErrors++;
      }
      
      if (product.usb_c_ports !== null && (product.usb_c_ports < 0 || product.usb_c_ports > 5)) {
        results.validationErrors.push({
          model,
          field: 'usb_c_ports',
          issue: 'Value out of range',
          value: product.usb_c_ports,
          expectedRange: '0-5'
        });
        typeErrors++;
      }
      
      // Validate app installation method enum
      if (product.app_installation_method !== null) {
        const validMethods = ['none', 'sideloading', 'play_store', 'both'];
        if (!validMethods.includes(product.app_installation_method)) {
          results.validationErrors.push({
            model,
            field: 'app_installation_method',
            issue: 'Invalid enum value',
            value: product.app_installation_method,
            validValues: validMethods
          });
          typeErrors++;
        }
      }
    });
    
    console.log(`   ${typeErrors === 0 ? '✅' : '❌'} Data type validation: ${typeErrors} errors`);
    results.summary.typeErrors = typeErrors;
  }

  /**
   * Validate business logic consistency
   */
  async validateBusinessLogic(products, results) {
    console.log('🧠 Validating business logic...');
    
    let logicWarnings = 0;
    
    products.forEach(product => {
      const model = product.model_number;
      
      // Warning: Google Play Store without EDLA certification
      if (product.google_play_store === true && product.google_edla_certified === false) {
        results.warnings.push({
          model,
          issue: 'Google Play Store enabled but not EDLA certified',
          severity: 'medium'
        });
        logicWarnings++;
      }
      
      // Warning: No app installation method but has Google Play Store
      if (product.google_play_store === true && product.app_installation_method === 'none') {
        results.warnings.push({
          model,
          issue: 'Google Play Store enabled but app installation method is "none"',
          severity: 'high'
        });
        logicWarnings++;
      }
      
      // Warning: OPS support but no HDMI ports
      if (product.ops_support === true && (!product.hdmi_ports || product.hdmi_ports === 0)) {
        results.warnings.push({
          model,
          issue: 'OPS support enabled but no HDMI ports',
          severity: 'low'
        });
        logicWarnings++;
      }
    });
    
    console.log(`   ${logicWarnings === 0 ? '✅' : '⚠️'} Business logic validation: ${logicWarnings} warnings`);
    results.summary.logicWarnings = logicWarnings;
  }

  /**
   * Validate data completeness
   */
  async validateCompleteness(products, results) {
    console.log('📊 Validating data completeness...');
    
    const completeness = {
      ops_support: 0,
      hdmi_ports: 0,
      browser_support: 0,
      google_play_store: 0,
      warranty_period: 0
    };
    
    products.forEach(product => {
      Object.keys(completeness).forEach(field => {
        if (product[field] !== null && product[field] !== undefined) {
          completeness[field]++;
        }
      });
    });
    
    Object.entries(completeness).forEach(([field, count]) => {
      const percentage = Math.round((count / products.length) * 100);
      console.log(`   ${field}: ${count}/${products.length} (${percentage}%)`);
      
      if (percentage < 80) {
        results.warnings.push({
          field,
          issue: `Low data completeness: ${percentage}%`,
          severity: 'medium'
        });
      }
    });
    
    results.summary.completeness = completeness;
  }

  /**
   * Generate validation summary
   */
  generateValidationSummary(results) {
    console.log('\n📋 Validation Summary:\n');
    
    const totalErrors = results.validationErrors.length;
    const totalWarnings = results.warnings.length;
    
    if (totalErrors === 0 && totalWarnings === 0) {
      console.log('🎉 VALIDATION PASSED - No issues found!');
    } else {
      if (totalErrors > 0) {
        console.log(`❌ VALIDATION FAILED - ${totalErrors} errors found`);
        console.log('\nErrors:');
        results.validationErrors.slice(0, 10).forEach(error => {
          console.log(`   ${error.model}: ${error.field} - ${error.issue}`);
        });
        if (totalErrors > 10) {
          console.log(`   ... and ${totalErrors - 10} more errors`);
        }
      }
      
      if (totalWarnings > 0) {
        console.log(`\n⚠️  ${totalWarnings} warnings found`);
        console.log('\nWarnings:');
        results.warnings.slice(0, 5).forEach(warning => {
          console.log(`   ${warning.model || warning.field}: ${warning.issue}`);
        });
        if (totalWarnings > 5) {
          console.log(`   ... and ${totalWarnings - 5} more warnings`);
        }
      }
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`   Total products validated: ${results.totalProducts}`);
    console.log(`   Connectivity errors: ${results.summary.connectivityErrors || 0}`);
    console.log(`   Specification errors: ${results.summary.specificationErrors || 0}`);
    console.log(`   Data type errors: ${results.summary.typeErrors || 0}`);
    console.log(`   Business logic warnings: ${results.summary.logicWarnings || 0}`);
  }
}

module.exports = DataValidationService;
