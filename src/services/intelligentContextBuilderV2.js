/**
 * Intelligent Context Builder V2
 * Updated to use separate columns instead of JSONB parsing
 * Provides more reliable and performant context building
 */

class IntelligentContextBuilderV2 {
  constructor(supabase) {
    this.supabase = supabase;
    
    // Define which data fields are relevant for different query types
    this.fieldMappings = {
      // Operating system and software queries
      os_software: {
        keywords: ['tizen', 'android', 'os', 'operating system', 'apps', 'software', 'browser', 'play store', 'edla', 'screen recording', 'recording', 'screen casting', 'casting'],
        fields: ['operating_system', 'os_version', 'built_in_apps', 'browser_support', 'google_play_store', 'google_edla_certified', 'app_installation_method', 'screen_casting_support', 'screen_recording_support'],
        includeSpecFields: [] // No longer needed - using direct columns
      },

      // Hardware and connectivity queries  
      hardware_connectivity: {
        keywords: ['ops', 'hdmi', 'vga', 'usb', 'ports', 'connectivity', 'wifi', 'bluetooth', 'touch', 'resolution', 'brightness', 'contrast', 'nits'],
        fields: ['connectivity', 'touch_points', 'touch_technology', 'resolution', 'display_size', 'brightness_nits', 'contrast_ratio', 'aspect_ratio', 'ops_support', 'hdmi_ports', 'vga_ports', 'usb_c_ports', 'wifi_support', 'bluetooth_support'],
        includeSpecFields: []
      },

      // Physical specifications
      physical_specs: {
        keywords: ['size', 'dimension', 'weight', 'mount', 'vesa', 'physical', 'install'],
        fields: ['display_size', 'dimensions', 'weight_kg', 'vesa_mount', 'aspect_ratio'],
        includeSpecFields: []
      },

      // Power and environmental
      power_environment: {
        keywords: ['power', 'consumption', 'watts', 'supply', 'temperature', 'environment'],
        fields: ['power_consumption_watts', 'power_supply', 'operating_temp_range'],
        includeSpecFields: []
      },

      // Business and use cases
      business_use_case: {
        keywords: ['target', 'market', 'education', 'corporate', 'meeting', 'classroom', 'use case', 'application', 'warranty', 'support', 'whiteboard', 'price', 'cost', 'launch'],
        fields: ['target_market', 'product_type', 'launch_date', 'price_range', 'warranty_period', 'whiteboard_application'],
        includeSpecFields: []
      },

      // Product comparison
      product_comparison: {
        keywords: ['compare', 'comparison', 'difference', 'vs', 'versus', 'which', 'best', 'recommend'],
        fields: ['model_number', 'brand', 'product_line', 'display_size', 'resolution', 'target_market', 'ops_support', 'hdmi_ports', 'google_play_store', 'browser_support'],
        includeSpecFields: []
      }
    };
  }

  /**
   * Analyze what type of data the user needs based on their message
   */
  analyzeDataNeeds(userMessage) {
    const message = userMessage.toLowerCase();
    const dataNeeds = [];

    Object.entries(this.fieldMappings).forEach(([category, config]) => {
      const hasKeyword = config.keywords.some(keyword => 
        message.includes(keyword.toLowerCase())
      );
      
      if (hasKeyword) {
        dataNeeds.push(category);
      }
    });

    // Default to comparison if no specific category detected
    if (dataNeeds.length === 0) {
      dataNeeds.push('product_comparison');
    }

    return dataNeeds;
  }

  /**
   * Build comprehensive structured data context using new columns
   */
  async buildStructuredContext(userMessage, detectedModel = null) {
    try {
      const dataNeeds = this.analyzeDataNeeds(userMessage);
      
      if (dataNeeds.length === 0) {
        return ''; // No structured data needed
      }

      // Get all products or specific product with new columns
      let products;
      if (detectedModel) {
        const { data, error } = await this.supabase
          .from('products')
          .select(`
            *,
            ops_support,
            hdmi_ports,
            vga_ports,
            usb_c_ports,
            wifi_support,
            bluetooth_support,
            browser_support,
            google_play_store,
            google_edla_certified,
            app_installation_method,
            screen_casting_support,
            screen_recording_support,
            warranty_period,
            whiteboard_application
          `)
          .eq('model_number', detectedModel.toUpperCase())
          .eq('status', 'active')
          .single();
        
        if (error) throw error;
        products = [data];
      } else {
        const { data, error } = await this.supabase
          .from('products')
          .select(`
            *,
            ops_support,
            hdmi_ports,
            vga_ports,
            usb_c_ports,
            wifi_support,
            bluetooth_support,
            browser_support,
            google_play_store,
            google_edla_certified,
            app_installation_method,
            screen_casting_support,
            screen_recording_support,
            warranty_period,
            whiteboard_application
          `)
          .eq('status', 'active')
          .order('model_number');
        
        if (error) throw error;
        products = data;
      }

      if (!products || products.length === 0) {
        return '';
      }

      // Build context for each needed data category
      let context = '';
      dataNeeds.forEach(category => {
        const config = this.fieldMappings[category];
        if (config) {
          context += this.buildCategoryContext(category, config, products, userMessage);
        }
      });

      // Add instruction for AI to use only provided information
      context += '\\nIMPORTANT: Use ONLY the product information listed above. Do not mention any models or specifications not explicitly provided.';

      return context;

    } catch (error) {
      console.error('Error building structured context:', error);
      return '';
    }
  }

  /**
   * Build context for a specific data category using new columns
   */
  buildCategoryContext(category, config, products, userMessage) {
    switch (category) {
      case 'os_software':
        return this.buildOSContextV2(products, userMessage);
      
      case 'hardware_connectivity':
        return this.buildHardwareContextV2(products, userMessage);
        
      case 'physical_specs':
        return this.buildPhysicalContextV2(products);
        
      case 'power_environment':
        return this.buildPowerContextV2(products);
        
      case 'business_use_case':
        return this.buildBusinessContextV2(products);
        
      case 'product_comparison':
        return this.buildComparisonContextV2(products);
        
      default:
        return '';
    }
  }

  /**
   * Build OS context using new columns (much cleaner!)
   */
  buildOSContextV2(products, userMessage) {
    let context = '\\nOPERATING SYSTEM & SOFTWARE FEATURES:\\n';
    
    // Group by OS
    const osGroups = {};
    products.forEach(p => {
      const os = p.operating_system || 'Unknown';
      if (!osGroups[os]) osGroups[os] = [];
      osGroups[os].push(p);
    });
    
    Object.keys(osGroups).sort().forEach(os => {
      context += `${os} models (${osGroups[os].length} models):\\n`;
      osGroups[os].forEach(p => {
        context += `• ${p.model_number} (${p.display_size}) - ${os}\\n`;
        
        // Add detailed software specifications using direct columns
        if (p.browser_support !== null) {
          context += `  - Browser: ${p.browser_support ? 'Yes (built-in)' : 'No'}\\n`;
        }
        
        if (p.google_play_store !== null) {
          context += `  - Google Play Store: ${p.google_play_store ? 'Yes' : 'No'}\\n`;
        }
        
        if (p.google_edla_certified !== null) {
          context += `  - Google EDLA Certified: ${p.google_edla_certified ? 'Yes' : 'No'}\\n`;
        }
        
        if (p.app_installation_method) {
          const methodMap = {
            'none': 'No app installation',
            'sideloading': 'Yes (sideloading only)',
            'play_store': 'Yes (Google Play Store)',
            'both': 'Yes (Play Store and sideloading)'
          };
          context += `  - Install Apps: ${methodMap[p.app_installation_method] || p.app_installation_method}\\n`;
        }
        
        if (p.screen_casting_support !== null) {
          context += `  - Screen Casting: ${p.screen_casting_support ? 'Yes' : 'No'}\\n`;
        }
        
        if (p.screen_recording_support !== null) {
          context += `  - Screen Recording: ${p.screen_recording_support ? 'Yes' : 'No'}\\n`;
        }
        
        if (p.whiteboard_application) {
          context += `  - Whiteboard App: ${p.whiteboard_application}\\n`;
        }
        
        if (p.warranty_period) {
          context += `  - Warranty: ${p.warranty_period}\\n`;
        }
        
        context += '\\n';
      });
    });
    
    return context;
  }

  /**
   * Build hardware context using new columns (much more reliable!)
   */
  buildHardwareContextV2(products, userMessage) {
    let context = '\\nHARDWARE & CONNECTIVITY SPECIFICATIONS:\\n';

    products.forEach(p => {
      context += `${p.model_number} (${p.display_size}):\\n`;

      // Display specifications
      context += `  Display: ${p.resolution || 'N/A'}`;
      if (p.aspect_ratio) context += ` ${p.aspect_ratio}`;
      if (p.brightness_nits) context += `, ${p.brightness_nits} nits`;
      if (p.contrast_ratio) context += `, ${p.contrast_ratio} contrast`;
      context += '\\n';

      // Touch specifications
      if (p.touch_points) {
        context += `  Touch: ${p.touch_points}-point ${p.touch_technology || 'touch'}\\n`;
      }

      // Connectivity using direct columns (much cleaner!)
      const ports = [];
      if (p.hdmi_ports && p.hdmi_ports > 0) ports.push(`${p.hdmi_ports} HDMI`);
      if (p.vga_ports && p.vga_ports > 0) ports.push(`${p.vga_ports} VGA`);
      if (p.usb_c_ports && p.usb_c_ports > 0) ports.push(`${p.usb_c_ports} USB-C`);
      if (p.ops_support) ports.push('OPS slot');

      const wireless = [];
      if (p.wifi_support) wireless.push('WiFi');
      if (p.bluetooth_support) wireless.push('Bluetooth');

      if (ports.length > 0) context += `  Ports: ${ports.join(', ')}\\n`;
      if (wireless.length > 0) context += `  Wireless: ${wireless.join(', ')}\\n`;

      // Handle explicit unavailability for specific queries
      if (userMessage) {
        const queryLower = userMessage.toLowerCase();
        const unavailablePorts = [];

        if (queryLower.includes('vga') && (!p.vga_ports || p.vga_ports === 0)) {
          unavailablePorts.push('VGA');
        }
        if (queryLower.includes('ops') && !p.ops_support) {
          unavailablePorts.push('OPS');
        }

        if (unavailablePorts.length > 0) {
          context += `  Not available: ${unavailablePorts.join(', ')}\\n`;
        }
      }

      context += '\\n';
    });

    return context;
  }

  /**
   * Build comparison context using new columns (crystal clear!)
   */
  buildComparisonContextV2(products) {
    let context = '\\nCOMPLETE PRODUCT LINEUP:\\n';

    products.forEach(p => {
      context += `${p.model_number}: ${p.brand} ${p.product_line} ${p.display_size} ${p.product_type}\\n`;
      context += `  OS: ${p.operating_system || 'N/A'}, Resolution: ${p.resolution || 'N/A'}`;
      if (p.touch_points) context += `, Touch: ${p.touch_points}-point`;
      context += '\\n';

      if (p.target_market) context += `  Target: ${p.target_market}\\n`;

      // Explicit OPS status (crystal clear for AI)
      if (p.ops_support !== null) {
        context += `  OPS Support: ${p.ops_support ? 'YES' : 'NO'}\\n`;
      }

      // Key differentiators using direct columns
      const keyFeatures = [];

      if (p.google_play_store) keyFeatures.push('Play Store');
      if (p.google_edla_certified) keyFeatures.push('EDLA');
      if (p.screen_recording_support) keyFeatures.push('Recording');
      if (p.ops_support) keyFeatures.push('OPS');
      if (p.vga_ports && p.vga_ports > 0) keyFeatures.push('VGA');

      if (keyFeatures.length > 0) {
        context += `  Features: ${keyFeatures.join(', ')}\\n`;
      }

      context += '\\n';
    });

    return context;
  }

  /**
   * Build business context using new columns
   */
  buildBusinessContextV2(products) {
    let context = '\\nBUSINESS & USE CASE INFORMATION:\\n';

    // Group by target market
    const marketGroups = {};
    products.forEach(p => {
      const markets = p.target_market ? p.target_market.split(',').map(m => m.trim()) : ['General'];
      markets.forEach(market => {
        if (!marketGroups[market]) marketGroups[market] = [];
        marketGroups[market].push(p);
      });
    });

    Object.keys(marketGroups).sort().forEach(market => {
      context += `${market} Applications:\\n`;
      marketGroups[market].forEach(p => {
        context += `• ${p.model_number} (${p.product_type}, ${p.display_size})\\n`;
        context += `  Target Market: ${p.target_market || 'General'}\\n`;

        // Business specifications using direct columns
        if (p.warranty_period) context += `  Warranty: ${p.warranty_period}\\n`;
        if (p.whiteboard_application) context += `  Whiteboard: ${p.whiteboard_application}\\n`;
        if (p.launch_date) context += `  Launch Date: ${p.launch_date}\\n`;
        if (p.price_range) context += `  Price Range: ${p.price_range}\\n`;

        context += '\\n';
      });
    });

    return context;
  }

  /**
   * Build physical context using existing logic (no changes needed)
   */
  buildPhysicalContextV2(products) {
    let context = '\\nPHYSICAL SPECIFICATIONS:\\n';

    products.forEach(p => {
      context += `${p.model_number}: ${p.display_size} ${p.product_type}\\n`;

      if (p.dimensions) {
        if (typeof p.dimensions === 'object') {
          const dims = [];
          if (p.dimensions.width) dims.push(`W: ${p.dimensions.width}`);
          if (p.dimensions.height) dims.push(`H: ${p.dimensions.height}`);
          if (p.dimensions.depth) dims.push(`D: ${p.dimensions.depth}`);
          if (dims.length > 0) context += `  Dimensions: ${dims.join(', ')}\\n`;
        } else {
          context += `  Dimensions: ${p.dimensions}\\n`;
        }
      }

      if (p.weight_kg) context += `  Weight: ${p.weight_kg}kg\\n`;
      if (p.vesa_mount) context += `  VESA Mount: ${p.vesa_mount}\\n`;
      if (p.aspect_ratio) context += `  Aspect Ratio: ${p.aspect_ratio}\\n`;

      context += '\\n';
    });

    return context;
  }

  /**
   * Build power context using existing logic (no changes needed)
   */
  buildPowerContextV2(products) {
    let context = '\\nPOWER & ENVIRONMENTAL SPECIFICATIONS:\\n';

    products.forEach(p => {
      context += `${p.model_number} (${p.display_size}):\\n`;

      if (p.power_consumption_watts) {
        context += `  Power Consumption: ${p.power_consumption_watts}W\\n`;
      }
      if (p.power_supply) {
        context += `  Power Supply: ${p.power_supply}\\n`;
      }
      if (p.operating_temp_range) {
        context += `  Operating Temperature: ${p.operating_temp_range}\\n`;
      }

      if (!p.power_consumption_watts && !p.power_supply && !p.operating_temp_range) {
        context += `  Power specifications not available\\n`;
      }

      context += '\\n';
    });

    return context;
  }
}

module.exports = IntelligentContextBuilderV2;
