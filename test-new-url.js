#!/usr/bin/env node

/**
 * Test New URL Configuration
 * Clear cache and test the new BASE_URL for document downloads
 */

require('dotenv').config();
const cacheService = require('./src/services/cacheService');

async function testNewUrl() {
  console.log('🔗 Testing New URL Configuration\n');
  
  try {
    // Check the BASE_URL
    console.log(`🌐 BASE_URL: ${process.env.BASE_URL || 'Not set (will use localhost:3000)'}`);
    
    // Clear manual cache
    console.log('\n🗑️ Clearing manual request cache...');
    const cacheKeys = [
      'I need a manual',
      'manual',
      'user manual',
      'setup guide',
      'documentation'
    ];
    
    for (const key of cacheKeys) {
      await cacheService.clearCache(key);
      console.log(`✅ Cleared cache for: "${key}"`);
    }
    
    // Test URL generation
    console.log('\n🔗 Testing URL generation...');
    const testDocId = '3f573e20-4b5d-429d-83d0-91a6f9dedeb4';
    const downloadUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/files/${testDocId}`;
    
    console.log(`📥 Sample download URL: ${downloadUrl}`);
    
    // Check if it's a valid external URL for Telegram
    const isValidForTelegram = downloadUrl.startsWith('https://') && 
                              !downloadUrl.includes('localhost') && 
                              !downloadUrl.includes('127.0.0.1');
    
    console.log(`✅ Valid for Telegram inline buttons: ${isValidForTelegram ? 'YES' : 'NO'}`);
    
    if (isValidForTelegram) {
      console.log('🎉 The bot will now show inline download buttons!');
    } else {
      console.log('⚠️  The bot will show text links only (no inline buttons)');
    }
    
    console.log('\n💡 Now test the manual request again to see the new URLs!');
    console.log('   Send "manual" or "user guide" to the bot');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testNewUrl()
  .then(() => console.log('\n✅ URL test completed'))
  .catch(error => console.error('💥 Test failed:', error));
